<?php
// Diagnostic file to check for BOM and encoding issues
ob_start();

echo "<h1>File Encoding Diagnostic</h1>";

// Check for BOM in minimal-session-test.php
$file = 'minimal-session-test.php';
if (file_exists($file)) {
    $content = file_get_contents($file);
    $firstBytes = substr($content, 0, 10);
    
    echo "<h2>$file Analysis:</h2>";
    echo "File size: " . strlen($content) . " bytes<br>";
    echo "First 10 bytes (hex): ";
    for ($i = 0; $i < min(10, strlen($content)); $i++) {
        echo dechex(ord($content[$i])) . " ";
    }
    echo "<br>";
    
    // Check for BOM
    if (substr($content, 0, 3) === "\xEF\xBB\xBF") {
        echo "❌ UTF-8 BOM detected!<br>";
    } else {
        echo "✅ No UTF-8 BOM<br>";
    }
    
    // Check for other invisible characters at start
    if (ord($content[0]) !== ord('<')) {
        echo "❌ File doesn't start with '&lt;' character<br>";
        echo "First character: " . htmlspecialchars($content[0]) . " (ASCII: " . ord($content[0]) . ")<br>";
    } else {
        echo "✅ File starts correctly<br>";
    }
}

// Test if we can start a session here
echo "<h2>Session Test:</h2>";
if (session_status() === PHP_SESSION_NONE) {
    if (session_start()) {
        echo "✅ Session started successfully<br>";
        echo "Session ID: " . session_id() . "<br>";
    } else {
        echo "❌ Session failed to start<br>";
    }
} else {
    echo "✅ Session already started<br>";
}

// Check headers sent
if (headers_sent($file, $line)) {
    echo "❌ Headers already sent from file: $file, line: $line<br>";
} else {
    echo "✅ No headers sent yet<br>";
}
?>
