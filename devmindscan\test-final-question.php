<?php
// Test final question submission
require("php/common.php");

echo "<h1>Final Question Test</h1>";

if (isset($_SESSION['id'])) {
    $userId = $_SESSION['id'];
    
    if (isset($_POST['simulate_q26'])) {
        echo "<h2>Simulating Q26 Submission...</h2>";
        
        // Simulate Q26 submission
        $update_status = updateQuery("users", "status='1'", "id='" . $userId . "'");
        $url = $webUrl . "reports.php";
        
        echo "Update status result: " . ($update_status ? 'Success' : 'Failed') . "<br>";
        echo "Redirect URL: " . $url . "<br>";
        echo "WebURL variable: " . $webUrl . "<br>";
        
        if ($update_status) {
            echo "<div style='color: green;'>✅ Status updated successfully!</div>";
            echo '<a href="' . $url . '" class="btn">Go to Reports</a><br>';
            
            // Test actual redirect
            echo "<div style='margin: 20px 0; padding: 10px; background: #f0f0f0;'>";
            echo "Testing redirect in 3 seconds...<br>";
            echo '<script>setTimeout(function(){ window.location.href = "' . $url . '"; }, 3000);</script>';
            echo "</div>";
        } else {
            echo "<div style='color: red;'>❌ Failed to update status</div>";
        }
    } else {
        echo "<h2>Current Status:</h2>";
        $userData = fetchData("status", "users", "id='" . $userId . "'");
        echo "User status: " . (isset($userData['status']) ? $userData['status'] : 'N/A') . "<br>";
        
        echo "<h2>Test Final Question Submission:</h2>";
        echo '<form method="post">';
        echo '<button type="submit" name="simulate_q26" class="btn">Simulate Q26 Completion</button>';
        echo '</form>';
    }
    
} else {
    echo "No session found. Please login first.<br>";
}
?>

<style>
.btn {
    display: inline-block;
    padding: 10px 20px;
    background: #007bff;
    color: white;
    text-decoration: none;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}
</style>
