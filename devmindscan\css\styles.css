html,
body {
    overflow-x: hidden;
}

.heading h2 {
    font-size: 180%;
    font-weight: 600;
}

.heading h1 {
    font-size: 275%;
}

.text-theme-dark {
    color: #221f20;
}

.heading h6 {
    font-weight: 500;
    font-size: 90%;
}

.font-weight-600 {
    font-weight: 600 !important;
}

.font-weight-500 {
    font-weight: 500 !important;
}

.bulb {
    width: 17%;
    margin-bottom: 3%;
}

.bg-theme-yellow {
    background-color: #fcc243;
}

.rounded-theme {
    border-radius: 12px !important;
}

.form-control {
    border-color: #fcc243 !important;
    height: calc(1.5em + 0.4rem);
}

.form-control:hover,
.form-control:focus {
    border-color: #fba43f !important;
    box-shadow: none !important;
}

.modal-backdrop {
    background-color: #ffffff;
}

span.left-arrow {
    font-size: 2.5rem;
    padding: 0rem 1rem;
    border-radius: 50%;
    background-color: #221f20;
    color: #f1c446;
    font-weight: 600;
}

span.flatpickr-day.selected {
    background-color: #fcc243;
    border-color: #fcc243;
}

.submit-wrapper button[type="submit"] {
    position: absolute;
    bottom: 0%;
    right: -1%;
}

.progress-bar {
    background-color: #fff6e3;
    width: 100%;
    padding: 1.8%;
    border-radius: 9px;
}

.welcome-page,
.question-page,
.post-quiz,
.filler-page {
    height: 100svh;
}

.question-number {
    letter-spacing: 0px;
    line-height: 19px;
    color: #dadae5;
}

.question {
    letter-spacing: 0px;
}

.social-links a img {
    width: 13%;
}


.progress-done {
    width: calc((100% * var(--w)) / 26);
    background: #fcc243;
    position: absolute;
    left: 0px;
    top: 0px;
    border-radius: 8px;
    height: 100%;
}

.left-arrow.left-back {
    transform: rotateY(180deg);
}

.option {
    color: #221f20;
    background: #fff;
    border: 1px solid #f1c446;
    border-radius: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
    font-size: 1.3rem;
    font-weight: 500;
    text-transform: uppercase;
}

[type="radio"]:checked+label {
    background: #f1c446;
}

label,
input,
select {
    color: #221f20;
}

.letter-spacing-1 {
    letter-spacing: 1px;
}

.option:hover {
    background-color: #f1c446;
}

.left-arrow.padding-less {
    padding: 0rem 1.1rem;
}

.filler-page h5 {
    letter-spacing: 0px;
    line-height: 32px;
}

.cirlce-border:before {
    content: '';
    position: absolute;
    width: 130%;
    height: 130%;
    background: url('../img/care-circle.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    top: -10%;
    left: -10%;
}

.line-height-70 {
    line-height: 70px;
}

.report-title h1 {
    letter-spacing: 0px;
    line-height: 70px;
    font-size: 2.6rem;
}

.border-bottom-image:before {
    content: '';
    position: absolute;
    width: 60%;
    height: 100%;
    background: url('../img/curve-border.png');
    background-repeat: no-repeat;
    background-size: contain;
    bottom: -100%;
    left: 16%;
}

.report-title p {
    font-size: 1rem;
    font-weight: 500;
    font-style: italic;
}

.divider {
    position: relative;
    width: 100%;
    height: 1%;
    background: #bdbcbc;
}

.button-wrapper {
    position: absolute;
    bottom: 38%;
    left: 17%;
}

.btn-theme-yellow {
    background: #fcc243;
    border-radius: 12px;
    font-size: 1rem;
}

.button-wrapper img {
    right: -4%;
    position: absolute;
    width: 10%;
    top: 26%;
}