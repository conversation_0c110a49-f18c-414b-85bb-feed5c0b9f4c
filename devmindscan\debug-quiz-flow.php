<?php
// Debug quiz flow and final redirect
require("php/common.php");

echo "<h1>Quiz Flow Debug</h1>";

// Handle status update request
if (isset($_GET['action']) && $_GET['action'] == 'update_status' && isset($_SESSION['id'])) {
    $updateResult = updateQuery("users", "status='1'", "id='" . $_SESSION['id'] . "'");
    if ($updateResult) {
        echo "<div style='color: green; margin: 10px 0;'>✅ Status updated to complete!</div>";
    } else {
        echo "<div style='color: red; margin: 10px 0;'>❌ Failed to update status</div>";
    }
}

if (isset($_SESSION['id'])) {
    $userId = $_SESSION['id'];
    
    // Get user data
    $userData = fetchData("all", "users", "id='" . $userId . "'");
    echo "<h2>User Status:</h2>";
    echo "User ID: " . $userId . "<br>";
    echo "Status: " . (isset($userData['status']) ? $userData['status'] : 'N/A') . "<br>";
    
    // Get all answers for this user
    $answers = fetchAll("q_id,answer,score", "answers", "user_id='" . $userId . "'", "q_id ASC");
    
    echo "<h2>Answered Questions:</h2>";
    if ($answers && is_array($answers) && count($answers) > 0) {
        foreach ($answers as $answer) {
            echo "Question " . $answer['q_id'] . ": Answer " . $answer['answer'] . " (Score: " . $answer['score'] . ")<br>";
        }
    } else {
        echo "No answers found<br>";
    }
    
    // Check what the next question should be
    $totalQuestions = 26;
    $answeredCount = (is_array($answers) && count($answers) > 0) ? count($answers) : 0;
    $lastAnswered = 0;
    
    if (is_array($answers) && count($answers) > 0) {
        $lastQuestion = end($answers)['q_id'];
        $lastAnswered = (int)str_replace('Q', '', $lastQuestion);
    }
    
    echo "<h2>Quiz Progress:</h2>";
    echo "Total Questions: " . $totalQuestions . "<br>";
    echo "Answered: " . $answeredCount . "<br>";
    echo "Last Answered: Q" . $lastAnswered . "<br>";
    
    if ($lastAnswered < $totalQuestions) {
        $nextQuestion = $lastAnswered + 1;
        echo "Next Question: Q" . $nextQuestion . "<br>";
        echo '<a href="' . $webUrl . 'question?qn=' . $nextQuestion . '" class="btn">Continue to Q' . $nextQuestion . '</a><br>';
        
        // Check if user is at the filler message point
        if ($lastAnswered == 21) {
            echo '<a href="' . $webUrl . 'filler-message" class="btn">Go to Filler Message</a><br>';
        }
    } else {
        echo "All questions completed!<br>";
        if ($userData['status'] == '1') {
            echo '<a href="' . $webUrl . 'reports.php" class="btn">View Reports</a><br>';
        } else {
            echo "Status not updated to completed. ";
            echo '<a href="?action=update_status" class="btn">Update Status to Complete</a><br>';
        }
    }
    
    // Test the final redirect manually
    echo "<h2>Manual Test:</h2>";
    echo '<a href="' . $webUrl . 'reports.php" class="btn">Test Reports Direct Link</a><br>';
    echo '<a href="reports.php" class="btn">Test Reports.php Direct</a><br>';
    
    // Show current webUrl
    echo "<h2>URL Info:</h2>";
    echo "WebURL: " . $webUrl . "<br>";
    echo "Reports URL: " . $webUrl . "reports<br>";
    
} else {
    echo "No session found. Please login first.<br>";
    echo '<a href="login.php">Login</a><br>';
}
?>

<style>
.btn {
    display: inline-block;
    padding: 8px 16px;
    margin: 5px;
    background: #007bff;
    color: white;
    text-decoration: none;
    border-radius: 4px;
}
</style>
