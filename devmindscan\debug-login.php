<?php
// Debug login processing
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Login Debug Information</h1>";

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h2>Form Data Received:</h2>";
    echo "<pre>";
    print_r($_POST);
    echo "</pre>";
    
    // Test database connection
    echo "<h2>Database Connection Test:</h2>";
    include("config/config.php");
    
    echo "Connecting to: $servername<br>";
    echo "Database: $dbname<br>";
    echo "Username: $username<br>";
    
    $conn = mysqli_connect($servername, $username, $password, $dbname);
    
    if (!$conn) {
        echo "<span style='color: red;'>❌ Database connection failed: " . mysqli_connect_error() . "</span><br>";
    } else {
        echo "<span style='color: green;'>✅ Database connected successfully</span><br>";
        
        // Test a simple query
        $result = mysqli_query($conn, "SHOW TABLES");
        if ($result) {
            echo "✅ Database query test passed<br>";
            echo "Tables in database:<br>";
            while ($row = mysqli_fetch_array($result)) {
                echo "- " . $row[0] . "<br>";
            }
        } else {
            echo "❌ Database query failed: " . mysqli_error($conn) . "<br>";
        }
        mysqli_close($conn);
    }
    
    // Check required fields
    echo "<h2>Required Fields Check:</h2>";
    $fields = ["user_name", "phone", "gender", "language","marital-status","age","emailid","location","profession","dateOfAssessment"];
    
    foreach ($fields as $field) {
        $status = isset($_POST[$field]) && !empty($_POST[$field]) ? "✅" : "❌";
        $value = isset($_POST[$field]) ? $_POST[$field] : "NOT SET";
        echo "$status $field: $value<br>";
    }
    
} else {
    echo "<p>No form data received. Please submit the login form.</p>";
}

echo "<h2>PHP Configuration:</h2>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Session Status: " . (session_status() === PHP_SESSION_ACTIVE ? "Active" : "Inactive") . "<br>";
echo "Error Reporting: " . error_reporting() . "<br>";

echo "<h2>Server Information:</h2>";
echo "Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "Request URI: " . $_SERVER['REQUEST_URI'] . "<br>";
echo "HTTP Host: " . $_SERVER['HTTP_HOST'] . "<br>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2 { color: #333; }
pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
</style>
