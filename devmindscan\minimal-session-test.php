<?php
// Start output buffering FIRST to prevent header issues
ob_start();

// Minimal session test to isolate the issue
ini_set('session.save_path', '/var/lib/php/sessions');

echo "<h1>Minimal Session Test</h1>";

// Check what's in the output buffer before session start
$bufferContent = ob_get_contents();
if (!empty($bufferContent)) {
    echo "⚠️ Output buffer contains: " . strlen($bufferContent) . " bytes<br>";
    echo "Content: " . htmlspecialchars($bufferContent) . "<br>";
}

// Start session with error checking
if (!session_start()) {
    echo "❌ Failed to start session<br>";
    echo "Error: " . error_get_last()['message'] . "<br>";
    exit;
}

echo "✅ Session started<br>";
echo "Session ID: '" . session_id() . "'<br>";
echo "Session save path: " . session_save_path() . "<br>";

// Simple test
if (!isset($_SESSION['count'])) {
    $_SESSION['count'] = 1;
    echo "First visit - counter set to 1<br>";
} else {
    $_SESSION['count']++;
    echo "Visit #" . $_SESSION['count'] . "<br>";
}

echo "<br><a href='" . $_SERVER['PHP_SELF'] . "'>Reload</a>";
?>
