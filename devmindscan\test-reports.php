<?php
// Test reports page access
require("php/common.php");

echo "<h1>Reports Page Test</h1>";

echo "Session ID: " . (isset($_SESSION['id']) ? $_SESSION['id'] : 'Not set') . "<br>";
echo "Web URL: " . $webUrl . "<br>";

if (isset($_SESSION['id'])) {
    $getuser = fetchData("all", "users", "id='" . $_SESSION['id'] . "'");
    echo "User status: " . (isset($getuser['status']) ? $getuser['status'] : 'Not found') . "<br>";
    
    if (isset($getuser['status']) && $getuser['status'] == "1") {
        echo "✅ User has completed quiz<br>";
        echo '<a href="' . $webUrl . 'reports">Go to Reports Page</a><br>';
        echo '<a href="' . $webUrl . 'reports.php">Go to Reports Page (with .php)</a><br>';
    } else {
        echo "❌ User has not completed quiz<br>";
    }
} else {
    echo "❌ No session found<br>";
}

echo "<br><a href='login.php'>Back to Login</a>";
?>
