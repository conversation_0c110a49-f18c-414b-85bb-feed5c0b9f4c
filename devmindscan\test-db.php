<?php
// Test database connection
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Database Connection Test</h1>";

include("config/config.php");

echo "<h2>Connection Details:</h2>";
echo "Server: $servername<br>";
echo "Database: $dbname<br>";
echo "Username: $username<br>";

echo "<h2>Testing Connection:</h2>";

try {
    // Test mysqli connection
    $conn = mysqli_connect($servername, $username, $password, $dbname);
    
    if (!$conn) {
        echo "❌ MySQLi connection failed: " . mysqli_connect_error() . "<br>";
    } else {
        echo "✅ MySQLi connection successful<br>";
        
        // Test users table
        $result = mysqli_query($conn, "SELECT COUNT(*) as count FROM users");
        if ($result) {
            $row = mysqli_fetch_assoc($result);
            echo "✅ Users table accessible. Record count: " . $row['count'] . "<br>";
        } else {
            echo "❌ Users table query failed: " . mysqli_error($conn) . "<br>";
        }
        
        mysqli_close($conn);
    }
    
    echo "<h2>Testing PDO Connection:</h2>";
    
    // Test PDO connection
    $pdo = new PDO("mysql:host=$servername;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ PDO connection successful<br>";
    
} catch (Exception $e) {
    echo "❌ Connection error: " . $e->getMessage() . "<br>";
}

echo "<h2>Network Test:</h2>";
$host = explode(':', $servername)[0];
$port = explode(':', $servername)[1] ?? 3306;

echo "Testing connection to $host:$port...<br>";

$connection = @fsockopen($host, $port, $errno, $errstr, 10);
if ($connection) {
    echo "✅ Network connection to $host:$port successful<br>";
    fclose($connection);
} else {
    echo "❌ Network connection failed: $errstr ($errno)<br>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2 { color: #333; }
</style>
