<?php
// Debug version of login process
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Login Process Debug</h1>";

// Start session
session_start();
echo "<h2>Session Started</h2>";
echo "Session ID: " . session_id() . "<br>";
echo "Session Status: " . session_status() . "<br>";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h2>Form Data Received:</h2>";
    echo "<pre>";
    print_r($_POST);
    echo "</pre>";
    
    // Include common functions
    include("php/common.php");
    
    echo "<h2>Testing Database Connection:</h2>";
    try {
        $conn = connect_db();
        if ($conn) {
            echo "✅ Database connected successfully<br>";
            mysqli_close($conn);
        } else {
            echo "❌ Database connection failed<br>";
        }
    } catch (Exception $e) {
        echo "❌ Database error: " . $e->getMessage() . "<br>";
    }
    
    echo "<h2>Testing Required Fields:</h2>";
    $fields = ["user_name", "phone", "gender", "language","marital-status","age","emailid","location","profession","dateOfAssessment"];
    $allFieldsPresent = checkRequired($fields);
    echo "All required fields present: " . ($allFieldsPresent ? "✅ Yes" : "❌ No") . "<br>";
    
    foreach ($fields as $field) {
        $status = isset($_POST[$field]) && !empty($_POST[$field]) ? "✅" : "❌";
        $value = isset($_POST[$field]) ? $_POST[$field] : "NOT SET";
        echo "$status $field: $value<br>";
    }
    
    if ($allFieldsPresent) {
        echo "<h2>Processing Login:</h2>";
        
        // Clear SQL injection
        $_POST = clearSqlInjection($_POST);
        echo "✅ SQL injection cleared<br>";
        
        // Check if user exists
        echo "<h3>Checking if user exists:</h3>";
        $userExists = checkUserPhone($_POST["phone"]);
        echo "User exists: " . $userExists . "<br>";
        
        if ($userExists == "true") {
            echo "<h3>Existing User Login:</h3>";
            $checkstatus = fetchData("id,status,language", "users", "phone='" . $_POST['phone'] . "'", "time_of_registration desc");
            echo "User data: <pre>";
            print_r($checkstatus);
            echo "</pre>";
            
            if (is_array($checkstatus) && isset($checkstatus['id'])) {
                echo "✅ User found with ID: " . $checkstatus['id'] . "<br>";
                
                // Set session
                echo "<h3>Setting Session:</h3>";
                echo "Before setSession - Session data: <pre>";
                print_r($_SESSION);
                echo "</pre>";
                
                setSession($checkstatus["id"]);
                
                echo "After setSession - Session data: <pre>";
                print_r($_SESSION);
                echo "</pre>";
                
                // Set start time
                if (!isset($_SESSION['start'])) {
                    $_SESSION['start'] = time();
                    echo "✅ Start time set: " . $_SESSION['start'] . "<br>";
                }
                
                // Test session persistence
                echo "<h3>Testing Session Persistence:</h3>";
                $sessionCheck = checkSession();
                echo "checkSession() result: " . $sessionCheck . "<br>";
                
                if ($sessionCheck == "true") {
                    echo "✅ Session is working correctly<br>";
                } else {
                    echo "❌ Session check failed<br>";
                }
                
            } else {
                echo "❌ User data fetch failed<br>";
            }
            
        } else {
            echo "<h3>New User Registration:</h3>";
            $id = "MS" . getRandomString(8);
            echo "Generated ID: $id<br>";
            
            // Try to insert user
            $insertResult = executeQuery("users", "id,username,email,phone,gender,language,age,location,marital_status,profession,test_date", "'" . $id . "','" . $_POST['user_name'] . "','" . $_POST['emailid'] . "','" . $_POST['phone'] . "','" . $_POST['gender'] . "','" . $_POST['language'] . "','" . $_POST['age'] . "','" . $_POST['location'] . "','" . $_POST['marital-status'] . "','" . $_POST['profession'] . "','" . $_POST['dateOfAssessment'] . "'");
            
            if ($insertResult) {
                echo "✅ User inserted successfully<br>";
                
                echo "<h3>Setting Session for New User:</h3>";
                echo "Before setSession - Session data: <pre>";
                print_r($_SESSION);
                echo "</pre>";
                
                setSession($id);
                
                echo "After setSession - Session data: <pre>";
                print_r($_SESSION);
                echo "</pre>";
                
                $sessionCheck = checkSession();
                echo "checkSession() result: " . $sessionCheck . "<br>";
                
            } else {
                echo "❌ User insertion failed<br>";
            }
        }
    }
    
} else {
    echo "<p>No form data received. Please submit the login form to test.</p>";
    echo '<form method="post">';
    echo '<input type="hidden" name="user_name" value="Test User">';
    echo '<input type="hidden" name="phone" value="1234567890">';
    echo '<input type="hidden" name="gender" value="male">';
    echo '<input type="hidden" name="language" value="en">';
    echo '<input type="hidden" name="marital-status" value="single">';
    echo '<input type="hidden" name="age" value="25">';
    echo '<input type="hidden" name="emailid" value="<EMAIL>">';
    echo '<input type="hidden" name="location" value="Test City">';
    echo '<input type="hidden" name="profession" value="Developer">';
    echo '<input type="hidden" name="dateOfAssessment" value="2025-07-06">';
    echo '<button type="submit">Test Login Process</button>';
    echo '</form>';
}

echo "<h2>Current Session State:</h2>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<h2>Session Configuration:</h2>";
echo "Session Save Path: " . session_save_path() . "<br>";
echo "Session Cookie Lifetime: " . ini_get('session.cookie_lifetime') . "<br>";
echo "Session GC Maxlifetime: " . ini_get('session.gc_maxlifetime') . "<br>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2, h3 { color: #333; }
pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
</style>
