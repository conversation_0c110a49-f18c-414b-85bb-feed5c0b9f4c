<?php ob_start(); session_start(); ?>
<h1>Ultra Clean Session Test</h1>
<?php
if (session_id()) {
    echo "✅ Session ID: " . session_id() . "<br>";
    
    if (!isset($_SESSION['clean_test'])) {
        $_SESSION['clean_test'] = 1;
        echo "First visit<br>";
    } else {
        $_SESSION['clean_test']++;
        echo "Visit #" . $_SESSION['clean_test'] . "<br>";
    }
} else {
    echo "❌ No session<br>";
}
?>
<a href="<?php echo $_SERVER['PHP_SELF']; ?>">Reload</a>
