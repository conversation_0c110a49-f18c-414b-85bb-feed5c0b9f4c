# Option A: Direct app deployment
version: '3.8'

services:
  mindscan-app:
    build:
      context: .
      dockerfile: Dockerfile.coolify
    container_name: mindscan-app
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.mindscan.rule=Host(`mindscan.mendingmind.org`)"
      - "traefik.http.routers.mindscan.entrypoints=web,websecure"
      - "traefik.http.routers.mindscan.tls.certresolver=letsencrypt"
      - "traefik.http.services.mindscan.loadbalancer.server.port=80"
    volumes:
      - ./generated_reports:/var/www/html/generated_reports
      - ./reports:/var/www/html/reports
    networks:
      - coolify
    environment:
      - PHP_INI_SCAN_DIR=/usr/local/etc/php/conf.d

networks:
  coolify:
    external: true

# Option B: If you want to keep your existing Apache on port 8081
# Uncomment the section below and comment out the section above
#
# version: '3.8'
#
# services:
#   mindscan-proxy:
#     image: nginx:alpine
#     container_name: mindscan-proxy
#     restart: unless-stopped
#     labels:
#       - "traefik.enable=true"
#       - "traefik.http.routers.mindscan.rule=Host(`mindscan.mendingmind.org`)"
#       - "traefik.http.routers.mindscan.entrypoints=web,websecure"
#       - "traefik.http.routers.mindscan.tls.certresolver=letsencrypt"
#       - "traefik.http.services.mindscan.loadbalancer.server.port=80"
#       - "traefik.http.services.mindscan.loadbalancer.server.url=http://host.docker.internal:8081"
#     extra_hosts:
#       - "host.docker.internal:host-gateway"
#     volumes:
#       - ./nginx-proxy.conf:/etc/nginx/nginx.conf:ro
#     networks:
#       - coolify
#
# networks:
#   coolify:
#     external: true
