
<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1080" height="1080" viewBox="0 0 1080 1080" xml:space="preserve">
<desc>Created with Fabric.js 5.2.4</desc>
<defs>
</defs>
<rect x="0" y="0" width="100%" height="100%" fill="transparent"></rect>
<g transform="matrix(1 0 0 1 540 540)" id="8522bed7-10c8-4df2-a773-2f97e368a20f"  >
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1; visibility: hidden;" vector-effect="non-scaling-stroke"  x="-540" y="-540" rx="0" ry="0" width="1080" height="1080" />
</g>
<g transform="matrix(1 0 0 1 540 540)" id="81880b9c-f14f-4499-9db7-15c49756f462"  >
</g>
<g transform="matrix(1.77 0 0 1.77 540.12 540.17)"  >
<g style="" vector-effect="non-scaling-stroke"   >
		<g transform="matrix(1 0 0 1 67.62 142.75)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(180,224,224); fill-rule: nonzero; opacity: 0.5;" vector-effect="non-scaling-stroke"  transform=" translate(-593.4, -560.8)" d="M 734.6 615.6 L 688.1 642.5 L 452.20000000000005 506 L 498.80000000000007 479.1 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 85.37 -234.53)" id="<Compound Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-611.15, -183.52)" d="M 568.6 158.2 C 569 156.6 570.3000000000001 156.1 571.7 156.89999999999998 L 590 167.09999999999997 C 591.6 167.99999999999997 592.8 166.89999999999998 592.6 164.79999999999995 L 590.1 140.59999999999997 C 589.9 139.09999999999997 590.8000000000001 138.19999999999996 592 138.89999999999998 L 606.3 146.79999999999998 C 603.4 142.29999999999998 601.5 136.7 601.5999999999999 131.6 C 601.8 123 607.4999999999999 119.1 614.3999999999999 122.89999999999999 C 621.2999999999998 126.69999999999999 626.6999999999998 136.89999999999998 626.4999999999999 145.5 C 626.3999999999999 150.6 624.2999999999998 154 621.2999999999998 155.1 L 635.5999999999998 163.1 C 636.7999999999998 163.79999999999998 637.5999999999998 165.6 637.3999999999997 167 L 633.8999999999997 187.8 C 633.5999999999998 189.60000000000002 634.7999999999997 192 636.2999999999997 192.9 L 654.5999999999997 203.1 C 656.0999999999997 203.9 657.2999999999997 205.79999999999998 657.6999999999997 207.7 L 660.4999999999997 245.6 L 611.1999999999997 218.2 L 561.7999999999997 190.7 z M 614.3000000000001 129.5 C 610.3000000000001 127.3 607.0000000000001 129.5 606.9000000000001 134.5 C 606.7 139.6 609.9000000000001 145.4 613.9000000000001 147.7 C 617.9000000000001 149.89999999999998 621.2 147.6 621.3000000000001 142.6 C 621.4000000000001 137.6 618.3000000000001 131.7 614.3000000000001 129.5 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 74.17 -39.25)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-599.95, -378.8)" d="M 692.5 615.5 L 501.7 504.4 C 488.4 496.7 480.5 482.4 480.7 466.59999999999997 L 483.7 158.09999999999997 C 483.8 144.69999999999996 498.59999999999997 135.59999999999997 509.9 141.99999999999997 L 697.7 248.09999999999997 C 711.1 255.69999999999996 719.2 269.9 719.2 285.7 L 718.9000000000001 599.4 C 718.9000000000001 612.9 703.9000000000001 622.1 692.5000000000001 615.5 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 74.17 -39.25)" id="<Compound Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-599.95, -378.79)" d="M 719.8 285.7 L 719.5 599.3 C 719.5 605.9 716 611.9 710.2 615.5 C 704.5 619 697.7 619.2 692.2 616 L 501.40000000000003 504.9 C 487.90000000000003 497.09999999999997 479.90000000000003 482.79999999999995 480.1 466.59999999999997 L 483.1 158.09999999999997 C 483.20000000000005 151.59999999999997 486.6 145.59999999999997 492.3 142.09999999999997 C 498 138.59999999999997 504.7 138.39999999999998 510.2 141.49999999999997 L 698 247.59999999999997 C 704.9 251.49999999999997 710.4 257.09999999999997 714.1 263.7 C 717.8000000000001 270.2 719.8000000000001 277.7 719.8000000000001 285.7 z M 501.99999999999994 503.9 L 692.8 615 C 698 618 704.3 617.8 709.6999999999999 614.5 C 715.0999999999999 611.2 718.3 605.5 718.3 599.4 L 718.5999999999999 285.79999999999995 C 718.5999999999999 269.99999999999994 710.6999999999999 256.09999999999997 697.3999999999999 248.69999999999996 L 509.59999999999985 142.49999999999994 C 504.39999999999986 139.59999999999994 498.1999999999999 139.89999999999995 492.89999999999986 143.09999999999994 C 487.4999999999999 146.39999999999995 484.29999999999984 151.99999999999994 484.1999999999999 157.99999999999994 L 481.1999999999999 466.59999999999997 C 481.09999999999985 474.49999999999994 483.09999999999985 481.99999999999994 486.6999999999999 488.49999999999994 C 490.2999999999999 494.79999999999995 495.4999999999999 500.09999999999997 501.9999999999999 503.8999999999999 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 63.72 -32.85)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(180,224,224); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-589.5, -385.2)" d="M 682.1 621.9 L 491.3 510.79999999999995 C 478 503.09999999999997 470 488.9 470.2 472.99999999999994 L 473.2 164.49999999999994 C 473.4 151.09999999999994 488.09999999999997 141.99999999999994 499.4 148.39999999999995 L 687.3 254.49999999999994 C 700.6999999999999 262.09999999999997 708.8 276.29999999999995 708.8 292.09999999999997 L 708.4 605.8 C 708.4 619.3 693.4 628.5 682.1 621.9 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 63.67 -32.85)" id="<Compound Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-589.45, -385.19)" d="M 709.3 292.1 L 709 605.7 C 709 612.3000000000001 705.6 618.3000000000001 699.8 621.9000000000001 C 694 625.4000000000001 687.3 625.6000000000001 681.8 622.4000000000001 L 490.9 511.30000000000007 C 477.4 503.50000000000006 469.4 489.20000000000005 469.59999999999997 473.00000000000006 L 472.59999999999997 164.50000000000006 C 472.7 158.00000000000006 476.09999999999997 152.00000000000006 481.79999999999995 148.50000000000006 C 487.59999999999997 145.00000000000006 494.19999999999993 144.80000000000007 499.69999999999993 147.90000000000006 L 687.5999999999999 254.00000000000006 C 694.4999999999999 257.90000000000003 699.9999999999999 263.50000000000006 703.6999999999999 270.1000000000001 C 707.4 276.6000000000001 709.3 284.1000000000001 709.3 292.1000000000001 z M 491.59999999999997 510.3 L 682.4 621.4 C 687.5 624.4 693.8 624.1999999999999 699.1999999999999 620.9 C 704.5999999999999 617.6 707.9 611.9 707.9 605.8 L 708.1999999999999 292.19999999999993 C 708.1999999999999 276.3999999999999 700.3 262.5999999999999 686.9999999999999 255.09999999999994 L 499.0999999999999 148.89999999999992 C 493.9999999999999 145.99999999999991 487.69999999999993 146.29999999999993 482.3999999999999 149.49999999999991 C 477.0999999999999 152.79999999999993 473.8999999999999 158.39999999999992 473.7999999999999 164.39999999999992 L 470.7999999999999 472.99999999999994 C 470.6999999999999 480.8999999999999 472.5999999999999 488.3999999999999 476.2999999999999 494.8999999999999 C 479.7999999999999 501.19999999999993 484.9999999999999 506.49999999999994 491.5999999999999 510.2999999999999 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 61.92 -49.65)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-587.7, -368.4)" d="M 489.6 154.9 L 690.9000000000001 268.6 L 687.3000000000001 581.9000000000001 L 484.50000000000006 467.4000000000001 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 61.97 -49.65)" id="<Compound Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-587.75, -368.4)" d="M 687.8 582.8 L 687.0999999999999 582.3 L 483.9999999999999 467.69999999999993 L 489.0999999999999 153.99999999999994 L 489.7999999999999 154.39999999999995 L 691.4999999999999 268.29999999999995 L 691.3999999999999 268.59999999999997 z M 484.99999999999994 467.09999999999997 L 686.9 581.0999999999999 L 690.5 268.8999999999999 L 490.1 155.79999999999993 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 121.27 -188.85)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-647.04, -229.2)" d="M 646.7 250.4 L 665 239.6 C 665 239.6 655.9 227.5 655.8 226.29999999999998 C 655.8 225.1 649.5999999999999 212.29999999999998 649.4 211.79999999999998 C 649.3 211.29999999999998 634.4 207.99999999999997 634.4 207.99999999999997 C 634.4 207.99999999999997 628.8 219.09999999999997 629.1 219.69999999999996 C 629.4 220.29999999999995 646.7 250.39999999999995 646.7 250.39999999999995 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 57.17 -255.85)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-582.95, -162.2)" d="M 556 161.4 L 570.1 156.6 L 581.4 165.9 L 577 143.5 L 592 138.9 L 609.9 154.8 L 604.6999999999999 176.8 L 575.9999999999999 185.5 C 575.9999999999999 185.5 565.7999999999998 182.1 565.3999999999999 181.2 C 565.0999999999999 180.39999999999998 555.9999999999999 161.39999999999998 555.9999999999999 161.39999999999998 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 81.32 -279.6)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-607.1, -138.45)" d="M 593.6 126.5 L 606.8000000000001 121.9 L 617.1 128.20000000000002 L 623.2 138.50000000000003 L 620 152.10000000000002 L 609.3 146.50000000000003 L 611.1999999999999 152.10000000000002 L 603.6999999999999 155.00000000000003 L 592.9999999999999 146.50000000000003 L 590.9999999999999 137.10000000000002 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 71.57 -229.73)" id="<Compound Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(240,201,59); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-597.35, -188.32)" d="M 554.7 162.9 C 555.1 161.4 556.4000000000001 160.9 557.9000000000001 161.70000000000002 L 576.2 171.9 C 577.7 172.70000000000002 579 171.6 578.8000000000001 169.5 L 576.2 145.4 C 576.1 143.8 577 143 578.2 143.6 L 592.5 151.6 C 589.6 147.1 587.7 141.5 587.8 136.4 C 588 127.80000000000001 593.6999999999999 123.9 600.5999999999999 127.7 C 607.3999999999999 131.5 612.8999999999999 141.6 612.6999999999999 150.2 C 612.5999999999999 155.29999999999998 610.4999999999999 158.7 607.4 159.89999999999998 L 621.6999999999999 167.79999999999998 C 622.9 168.49999999999997 623.8 170.39999999999998 623.5999999999999 171.7 L 620.0999999999999 192.5 C 619.8 194.3 620.8999999999999 196.8 622.4999999999999 197.6 L 640.7999999999998 207.79999999999998 C 642.2999999999998 208.6 643.4999999999999 210.49999999999997 643.7999999999998 212.49999999999997 L 646.6999999999998 250.39999999999998 L 597.2999999999998 222.89999999999998 L 547.9999999999999 195.39999999999998 z M 600.4000000000001 134.20000000000002 C 596.4000000000001 132.00000000000003 593.0000000000001 134.3 592.9000000000001 139.3 C 592.8000000000001 144.3 596.0000000000001 150.20000000000002 600.0000000000001 152.4 C 604.0000000000001 154.6 607.3000000000001 152.4 607.4000000000001 147.3 C 607.5000000000001 142.3 604.3000000000001 136.5 600.4000000000001 134.20000000000002 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 71.57 -229.55)" id="<Compound Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-597.35, -188.5)" d="M 647.3 251.3 L 547.4 195.70000000000002 L 554.1999999999999 162.8 C 554.4999999999999 161.9 554.9999999999999 161.3 555.6999999999999 161 C 556.4 160.7 557.3 160.8 558.0999999999999 161.2 L 576.3999999999999 171.39999999999998 C 576.8999999999999 171.7 577.3999999999999 171.7 577.6999999999998 171.49999999999997 C 578.0999999999998 171.19999999999996 578.2999999999998 170.49999999999997 578.2999999999998 169.59999999999997 L 575.6999999999998 145.39999999999998 C 575.5999999999998 144.39999999999998 575.8999999999999 143.59999999999997 576.5999999999998 143.09999999999997 C 577.0999999999998 142.79999999999995 577.7999999999998 142.79999999999995 578.3999999999997 143.19999999999996 L 591.0999999999998 150.19999999999996 C 588.5999999999998 145.79999999999995 587.1999999999998 140.79999999999995 587.2999999999998 136.39999999999995 C 587.3999999999999 131.79999999999995 588.9999999999999 128.29999999999995 591.8999999999999 126.69999999999995 C 594.3999999999999 125.19999999999995 597.4999999999999 125.39999999999995 600.7999999999998 127.19999999999995 C 607.7999999999998 131.09999999999994 613.3999999999999 141.49999999999994 613.1999999999998 150.19999999999993 C 613.0999999999998 154.89999999999992 611.3999999999999 158.29999999999993 608.4999999999998 159.89999999999992 L 621.9999999999998 167.39999999999992 C 623.2999999999997 168.19999999999993 624.2999999999997 170.19999999999993 624.0999999999998 171.79999999999993 L 620.5999999999998 192.59999999999994 C 620.2999999999998 194.19999999999993 621.3999999999997 196.39999999999995 622.6999999999998 197.19999999999993 L 641.0999999999998 207.39999999999992 C 642.5999999999998 208.19999999999993 643.9999999999998 210.29999999999993 644.2999999999998 212.39999999999992 z M 548.5999999999999 195.20000000000002 L 646.1999999999999 249.5 L 643.3 212.6 C 643 210.79999999999998 641.9 209 640.5999999999999 208.29999999999998 L 622.3 198.1 C 620.5 197.1 619.3 194.5 619.5999999999999 192.5 L 623.0999999999999 171.7 C 623.3 170.5 622.4999999999999 168.89999999999998 621.4999999999999 168.29999999999998 L 606.2999999999998 159.79999999999998 L 607.2999999999998 159.39999999999998 C 610.2999999999998 158.29999999999998 612.0999999999998 154.89999999999998 612.1999999999998 150.2 C 612.3999999999999 141.79999999999998 607.0999999999998 131.89999999999998 600.3999999999999 128.1 C 597.3999999999999 126.5 594.5999999999999 126.3 592.3999999999999 127.6 C 589.8999999999999 129 588.3999999999999 132.2 588.2999999999998 136.4 C 588.1999999999998 141.20000000000002 589.8999999999999 146.70000000000002 592.9999999999999 151.3 L 594.0999999999999 153 L 577.9999999999999 144.1 C 577.7999999999998 144 577.3999999999999 143.79999999999998 577.0999999999999 144 C 576.8 144.2 576.6999999999999 144.7 576.8 145.4 L 579.3 169.5 C 579.4 170.8 579.0999999999999 171.9 578.3 172.4 C 577.5999999999999 172.8 576.8 172.8 576 172.3 L 557.7 162.10000000000002 C 557.1 161.8 556.5 161.70000000000002 556.1 161.90000000000003 Q 555.5 162.20000000000005 555.2 163.10000000000002 z M 603.0999999999999 153.70000000000002 Q 601.5999999999999 153.70000000000002 599.8999999999999 152.8 C 595.7999999999998 150.5 592.4999999999999 144.4 592.5999999999999 139.3 C 592.6999999999999 136.5 593.6999999999999 134.4 595.3999999999999 133.4 C 596.8999999999999 132.6 598.7999999999998 132.70000000000002 600.7999999999998 133.8 C 604.8999999999999 136.10000000000002 608.1999999999998 142.20000000000002 608.0999999999998 147.3 C 607.9999999999998 150.10000000000002 606.9999999999998 152.20000000000002 605.2999999999998 153.20000000000002 Q 604.2999999999998 153.70000000000002 603.0999999999998 153.70000000000002 z M 597.5999999999999 133.9 Q 596.6999999999999 133.9 595.8999999999999 134.3 C 594.4999999999999 135.10000000000002 593.6999999999998 136.9 593.5999999999999 139.3 C 593.4999999999999 144.10000000000002 596.5999999999999 149.8 600.3999999999999 151.9 C 601.9999999999999 152.9 603.5999999999999 153 604.7999999999998 152.3 C 606.1999999999998 151.5 606.9999999999999 149.70000000000002 607.0999999999998 147.3 C 607.1999999999998 142.5 604.1999999999998 136.8 600.2999999999998 134.60000000000002 Q 598.8999999999999 133.90000000000003 597.5999999999998 133.90000000000003 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -1.83 -187.8)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-523.95, -230.25)" d="M 511 237.7 L 536.9 252.7 L 536.9 222.79999999999998 L 511 207.79999999999998 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -1.88 -187.78)" id="<Compound Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-523.9, -230.26)" d="M 537.1 222.3 C 537.3000000000001 222.4 537.3000000000001 222.60000000000002 537.3000000000001 222.8 L 537.3000000000001 252.70000000000002 C 537.3000000000001 252.8 537.3000000000001 253.00000000000003 537.1 253.10000000000002 Q 537 253.20000000000002 536.9 253.20000000000002 Q 536.6999999999999 253.20000000000002 536.6 253.10000000000002 L 510.70000000000005 238.10000000000002 C 510.6 238.10000000000002 510.50000000000006 237.90000000000003 510.50000000000006 237.70000000000002 L 510.50000000000006 207.8 C 510.50000000000006 207.60000000000002 510.6000000000001 207.5 510.70000000000005 207.4 C 510.90000000000003 207.3 511.00000000000006 207.3 511.20000000000005 207.4 z M 536.4 223 L 511.4 208.6 L 511.4 237.4 L 536.4 251.8 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 77.17 -153.2)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(180,224,224); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-602.95, -264.85)" d="M 540.9 232.3 L 665 303.9 L 665 297.5 L 540.9 225.8 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 47.62 -159.05)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(180,224,224); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-573.4, -259)" d="M 540.9 243.5 L 605.9 281 L 605.9 274.6 L 540.9 237.00000000000003 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 77.17 -130.8)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(180,224,224); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-602.95, -287.25)" d="M 540.9 254.7 L 665 326.29999999999995 L 665 319.9 L 540.9 248.2 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -1.83 -101.1)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-523.95, -316.95)" d="M 511 324.4 L 536.9 339.4 L 536.9 309.5 L 511 294.5 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -1.88 -101.08)" id="<Compound Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-523.9, -316.96)" d="M 537.1 309.1 C 537.3000000000001 309.20000000000005 537.3000000000001 309.3 537.3000000000001 309.5 L 537.3000000000001 339.4 C 537.3000000000001 339.59999999999997 537.3000000000001 339.7 537.1 339.79999999999995 Q 537 339.9 536.9 339.9 Q 536.6999999999999 339.9 536.6 339.79999999999995 L 510.70000000000005 324.9 C 510.6 324.79999999999995 510.50000000000006 324.59999999999997 510.50000000000006 324.4 L 510.50000000000006 294.5 C 510.50000000000006 294.4 510.6000000000001 294.2 510.70000000000005 294.1 C 510.90000000000003 294 511.00000000000006 294 511.20000000000005 294.1 z M 536.4 309.8 L 511.4 295.40000000000003 L 511.4 324.20000000000005 L 536.4 338.6 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 77.17 -66.4)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(180,224,224); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-602.95, -351.65)" d="M 540.9 319 L 665 390.7 L 665 384.2 L 540.9 312.6 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 47.62 -72.25)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(180,224,224); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-573.4, -345.8)" d="M 540.9 330.2 L 605.9 367.8 L 605.9 361.3 L 540.9 323.8 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 77.17 -44.1)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(180,224,224); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-602.95, -373.95)" d="M 540.9 341.4 L 665 413 L 665 406.6 L 540.9 334.90000000000003 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -1.83 -57.7)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(240,201,59); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-523.95, -360.35)" d="M 511 367.8 L 536.9 382.8 L 536.9 352.8 L 511 337.90000000000003 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -1.88 -57.73)" id="<Compound Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-523.9, -360.31)" d="M 537.1 352.4 C 537.3000000000001 352.5 537.3000000000001 352.7 537.3000000000001 352.79999999999995 L 537.3000000000001 382.79999999999995 C 537.3000000000001 382.9 537.3000000000001 383.09999999999997 537.1 383.19999999999993 Q 537 383.19999999999993 536.9 383.19999999999993 Q 536.6999999999999 383.19999999999993 536.6 383.19999999999993 L 510.70000000000005 368.19999999999993 C 510.6 368.0999999999999 510.50000000000006 367.99999999999994 510.50000000000006 367.79999999999995 L 510.50000000000006 337.9 C 510.50000000000006 337.7 510.6000000000001 337.59999999999997 510.70000000000005 337.5 C 510.90000000000003 337.4 511.00000000000006 337.4 511.20000000000005 337.5 z M 536.4 353.09999999999997 L 511.4 338.7 L 511.4 367.5 L 536.4 381.9 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 77.17 -23.1)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-602.95, -394.95)" d="M 540.9 362.4 L 665 434 L 665 427.6 L 540.9 355.90000000000003 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 47.62 -28.95)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-573.4, -389.1)" d="M 540.9 373.6 L 605.9 411.1 L 605.9 404.70000000000005 L 540.9 367.1 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 77.17 -0.7)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-602.95, -417.35)" d="M 540.9 384.8 L 665 456.4 L 665 450 L 540.9 378.3 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -1.6 -56.66)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-524.18, -361.39)" d="M 531.2 354.2 L 522.4000000000001 364.5 L 517.4000000000001 353.8 C 517.1000000000001 353.1 516.2 352.8 515.5000000000001 353.1 C 514.8000000000001 353.5 514.4000000000001 354.3 514.8000000000001 355.1 L 521.6 369.8 L 533.4 356.1 C 533.9 355.5 533.8 354.6 533.1999999999999 354 C 532.5999999999999 353.5 531.6999999999999 353.6 531.1999999999999 354.2 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -1.83 -145.2)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(240,201,59); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-523.95, -272.85)" d="M 511 280.3 L 536.9 295.3 L 536.9 265.40000000000003 L 511 250.40000000000003 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -1.88 -145.18)" id="<Compound Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-523.9, -272.86)" d="M 537.1 264.9 C 537.3000000000001 265 537.3000000000001 265.2 537.3000000000001 265.4 L 537.3000000000001 295.29999999999995 C 537.3000000000001 295.49999999999994 537.3000000000001 295.59999999999997 537.1 295.69999999999993 Q 537 295.79999999999995 536.9 295.79999999999995 Q 536.6999999999999 295.79999999999995 536.6 295.69999999999993 L 510.70000000000005 280.69999999999993 C 510.6 280.69999999999993 510.50000000000006 280.49999999999994 510.50000000000006 280.29999999999995 L 510.50000000000006 250.39999999999995 C 510.50000000000006 250.19999999999996 510.6000000000001 250.09999999999994 510.70000000000005 249.99999999999994 C 510.90000000000003 249.89999999999995 511.00000000000006 249.89999999999995 511.20000000000005 249.99999999999994 z M 536.4 265.59999999999997 L 511.4 251.19999999999996 L 511.4 279.99999999999994 L 536.4 294.3999999999999 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 77.17 -110.6)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-602.95, -307.45)" d="M 540.9 274.9 L 665 346.5 L 665 340.1 L 540.9 268.40000000000003 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 47.62 -116.4)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-573.4, -301.65)" d="M 540.9 286.1 L 605.9 323.70000000000005 L 605.9 317.20000000000005 L 540.9 279.6 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 77.17 -88.2)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-602.95, -329.85)" d="M 540.9 297.3 L 665 368.9 L 665 362.5 L 540.9 290.8 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -1.6 -144.16)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-524.18, -273.89)" d="M 531.2 266.7 L 522.4000000000001 277 L 517.4000000000001 266.3 C 517.1000000000001 265.6 516.2 265.3 515.5000000000001 265.6 C 514.8000000000001 266 514.4000000000001 266.8 514.8000000000001 267.6 L 521.6 282.3 L 533.4 268.6 C 533.9 268 533.8 267.1 533.1999999999999 266.5 C 532.5999999999999 266 531.6999999999999 266.1 531.1999999999999 266.7 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -1.83 -12.65)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-523.95, -405.4)" d="M 511 412.9 L 536.9 427.79999999999995 L 536.9 397.9 L 511 383 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -1.88 -12.65)" id="<Compound Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-523.9, -405.4)" d="M 537.1 397.5 C 537.3000000000001 397.6 537.3000000000001 397.7 537.3000000000001 397.9 L 537.3000000000001 427.79999999999995 C 537.3000000000001 427.99999999999994 537.3000000000001 428.19999999999993 537.1 428.29999999999995 Q 537 428.29999999999995 536.9 428.29999999999995 Q 536.6999999999999 428.29999999999995 536.6 428.29999999999995 L 510.70000000000005 413.29999999999995 C 510.6 413.19999999999993 510.50000000000006 413.09999999999997 510.50000000000006 412.9 L 510.50000000000006 383 C 510.50000000000006 382.8 510.6000000000001 382.6 510.70000000000005 382.5 C 510.90000000000003 382.5 511.00000000000006 382.5 511.20000000000005 382.5 z M 536.4 398.2 L 511.4 383.9 L 511.4 412.59999999999997 L 536.4 426.99999999999994 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 77.17 22)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(180,224,224); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-602.95, -440.05)" d="M 540.9 407.4 L 665 479.09999999999997 L 665 472.59999999999997 L 540.9 401 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 47.62 16.15)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(180,224,224); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-573.4, -434.2)" d="M 540.9 418.6 L 605.9 456.20000000000005 L 605.9 449.80000000000007 L 540.9 412.20000000000005 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 77.17 44.4)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(180,224,224); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-602.95, -462.45)" d="M 540.9 429.8 L 665 501.5 L 665 495 L 540.9 423.4 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -1.83 29.95)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(240,201,59); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-523.95, -448)" d="M 511 455.5 L 536.9 470.4 L 536.9 440.5 L 511 425.6 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -1.88 29.97)" id="<Compound Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-523.9, -448.01)" d="M 537.1 440.1 C 537.3000000000001 440.20000000000005 537.3000000000001 440.40000000000003 537.3000000000001 440.5 L 537.3000000000001 470.4 C 537.3000000000001 470.59999999999997 537.3000000000001 470.79999999999995 537.1 470.9 Q 537 470.9 536.9 470.9 Q 536.6999999999999 470.9 536.6 470.9 L 510.70000000000005 455.9 C 510.6 455.79999999999995 510.50000000000006 455.7 510.50000000000006 455.5 L 510.50000000000006 425.6 C 510.50000000000006 425.40000000000003 510.6000000000001 425.20000000000005 510.70000000000005 425.20000000000005 C 510.90000000000003 425.1 511.00000000000006 425.1 511.20000000000005 425.20000000000005 z M 536.4 440.8 L 511.4 426.40000000000003 L 511.4 455.1 L 536.4 469.5 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 77.17 64.6)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-602.95, -482.65)" d="M 540.9 450 L 665 521.7 L 665 515.2 L 540.9 443.6 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 47.62 58.75)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-573.4, -476.8)" d="M 540.9 461.2 L 605.9 498.8 L 605.9 492.40000000000003 L 540.9 454.8 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 77.17 87)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-602.95, -505.05)" d="M 540.9 472.4 L 665 544.1 L 665 537.6 L 540.9 466 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -1.6 31.04)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-524.18, -449.09)" d="M 531.2 441.9 L 522.4000000000001 452.2 L 517.4000000000001 441.5 C 517.1000000000001 440.8 516.2 440.5 515.5000000000001 440.8 C 514.8000000000001 441.1 514.4000000000001 442 514.8000000000001 442.7 L 521.6 457.5 L 533.4 443.8 C 533.9 443.2 533.8 442.2 533.1999999999999 441.7 C 532.5999999999999 441.2 531.6999999999999 441.3 531.1999999999999 441.9 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -45.18 172.7)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(180,224,224); fill-rule: nonzero; opacity: 0.5;" vector-effect="non-scaling-stroke"  transform=" translate(-480.6, -590.75)" d="M 480.6 555.9 L 420.20000000000005 590.6999999999999 L 480.6 625.5999999999999 L 541 590.6999999999999 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -45.18 168.35)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-480.6, -586.4)" d="M 541 572.7 L 480.6 551.5 L 420.20000000000005 572.7 L 420.20000000000005 586.4000000000001 L 480.6 621.3000000000001 L 541 586.4000000000001 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -15.03 178.95)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(180,224,224); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-510.75, -597)" d="M 480.6 607.6 L 480.5 621.3000000000001 L 541 586.4000000000001 L 541 572.7 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -15.03 178.9)" id="<Compound Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-510.75, -596.95)" d="M 480 622.1 L 480.1 607.3000000000001 L 480.3 607.1 L 541.5 571.8000000000001 L 541.5 586.7 z M 481.1 607.8000000000001 L 481 620.4000000000001 L 540.5 586.1000000000001 L 540.5 573.5000000000001 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -45.18 168.37)" id="<Compound Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-480.6, -586.41)" d="M 420 586.7 Q 419.8 586.6 419.8 586.4000000000001 L 419.8 572.7 C 419.8 572.5 419.90000000000003 572.4000000000001 420 572.3000000000001 L 480.5 551.2 Q 480.6 551.1 480.7 551.2 L 541.1 572.3000000000001 C 541.3000000000001 572.4000000000001 541.4 572.5000000000001 541.4 572.7 L 541.4 586.4000000000001 Q 541.4 586.6000000000001 541.1999999999999 586.7 L 480.79999999999995 621.6 Q 480.69999999999993 621.7 480.59999999999997 621.7 Q 480.49999999999994 621.7 480.4 621.6 z M 420.6 586.2 L 480.6 620.8000000000001 L 540.6 586.2 L 540.6 572.9000000000001 L 480.6 551.9000000000001 L 420.6 572.9000000000001 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -45.18 154.65)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-480.6, -572.7)" d="M 480.6 537.8 L 420.20000000000005 572.6999999999999 L 480.6 607.5999999999999 L 541 572.6999999999999 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -45.18 154.62)" id="<Compound Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-480.6, -572.66)" d="M 420 573 Q 419.8 572.9 419.8 572.7 Q 419.8 572.5 420 572.4000000000001 L 480.4 537.5000000000001 Q 480.59999999999997 537.4000000000001 480.79999999999995 537.5000000000001 L 541.1999999999999 572.4000000000001 Q 541.4 572.5000000000001 541.4 572.7 Q 541.4 572.9000000000001 541.1999999999999 573 L 480.79999999999995 607.9 Q 480.69999999999993 607.9 480.59999999999997 607.9 Q 480.49999999999994 607.9 480.4 607.9 z M 420.9 572.7 L 480.59999999999997 607.1 L 540.1999999999999 572.7 L 480.5999999999999 538.2 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -45.18 193.25)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-480.6, -611.3)" d="M 480.2 607.2 L 480.2 615.4000000000001 L 481 615.4000000000001 L 481 607.2 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -35.12 151.76)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(240,201,59); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-490.66, -569.81)" d="M 474.4 560 C 475.09999999999997 557.9 474 555.7 471.9 555.1 C 469.9 554.6 467.79999999999995 555.9 467.2 557.8000000000001 L 461.2 575.8000000000001 C 459.59999999999997 580.6 463.8 585.5000000000001 468.8 584.5000000000001 L 517.4 575.6000000000001 C 519.4 575.2000000000002 520.8 573.3000000000002 520.4 571.2000000000002 C 520 569.2000000000002 518.1 567.8000000000002 516 568.2000000000002 L 473.5 576.0000000000001 C 471.4 576.4000000000001 469.6 574.3000000000001 470.3 572.3000000000001 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -35.13 151.78)" id="<Compound Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-490.65, -569.83)" d="M 470.7 572.4 C 470.4 573.1999999999999 470.59999999999997 574.1 471.2 574.8 C 471.7 575.5 472.59999999999997 575.8 473.5 575.5999999999999 L 515.9 567.8 C 518.1999999999999 567.4 520.4 568.9 520.8 571.0999999999999 C 521.1999999999999 573.3999999999999 519.6999999999999 575.5999999999999 517.4 575.9999999999999 L 468.9 584.8999999999999 Q 468.2 584.9999999999999 467.59999999999997 584.9999999999999 C 465.49999999999994 584.9999999999999 463.59999999999997 584.1999999999999 462.2 582.5999999999999 C 460.5 580.6999999999999 460 578.0999999999999 460.8 575.5999999999999 L 466.8 557.6999999999999 C 467.5 555.4999999999999 469.90000000000003 554.1999999999999 472 554.8 C 473.1 555.0999999999999 474 555.8 474.5 556.8 C 475.1 557.9 475.1 559 474.8 560.0999999999999 z M 470 572.1999999999999 L 474.1 559.9 C 474.40000000000003 559 474.3 558.1 473.8 557.1999999999999 C 473.40000000000003 556.4 472.7 555.8 471.8 555.4999999999999 C 470 554.9999999999999 468.1 556.0999999999999 467.5 557.9999999999999 L 461.5 575.8999999999999 C 460.8 578.0999999999999 461.3 580.3999999999999 462.8 582.0999999999999 C 464.3 583.8 466.5 584.5999999999999 468.7 584.1999999999999 L 517.3 575.3 C 519.0999999999999 574.9 520.4 573.0999999999999 520 571.3 C 519.7 569.5 517.9 568.1999999999999 516.1 568.5999999999999 L 473.6 576.3999999999999 C 472.5 576.5999999999999 471.3 576.1999999999998 470.6 575.2999999999998 C 469.8 574.3999999999999 469.6 573.2999999999998 470 572.1999999999998 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 50.72 232.6)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(180,224,224); fill-rule: nonzero; opacity: 0.5;" vector-effect="non-scaling-stroke"  transform=" translate(-576.5, -650.65)" d="M 576.5 615.8 L 516.1 650.5999999999999 L 576.5 685.4999999999999 L 636.9 650.5999999999999 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 50.72 228.25)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-576.5, -646.3)" d="M 636.9 632.6 L 576.5 611.4 L 516.1 632.6 L 516.1 646.3000000000001 L 576.5 681.2 L 636.9 646.3000000000001 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 80.87 238.85)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(180,224,224); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-606.65, -656.9)" d="M 576.5 667.5 L 576.4 681.2 L 636.9 646.3000000000001 L 636.9 632.6 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 80.87 238.8)" id="<Compound Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-606.65, -656.85)" d="M 575.9 682 L 576 667.2 L 576.2 667 L 637.4000000000001 631.7 L 637.4000000000001 646.6 L 637.2 646.7 z M 577 667.7 L 576.9 680.3000000000001 L 636.4 646.0000000000001 L 636.4 633.4000000000001 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 50.72 228.27)" id="<Compound Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-576.5, -646.31)" d="M 515.9 646.6 Q 515.6999999999999 646.5 515.6999999999999 646.3000000000001 L 515.6999999999999 632.6 C 515.6999999999999 632.4 515.8 632.3000000000001 515.9999999999999 632.2 L 576.3999999999999 611.1 Q 576.4999999999999 611 576.5999999999999 611.1 L 636.9999999999999 632.2 C 637.1999999999999 632.3000000000001 637.2999999999998 632.4000000000001 637.2999999999998 632.6 L 637.2999999999998 646.3000000000001 Q 637.2999999999998 646.5000000000001 637.0999999999998 646.6 L 576.6999999999998 681.5 Q 576.5999999999998 681.6 576.4999999999998 681.6 Q 576.3999999999997 681.6 576.2999999999997 681.5 z M 516.5 646.1 L 576.5 680.7 L 636.5 646.1 L 636.5 632.8000000000001 L 576.5 611.8000000000001 L 516.5 632.8000000000001 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 50.72 214.55)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-576.5, -632.6)" d="M 576.5 597.7 L 516.1 632.6 L 576.5 667.5 L 636.9 632.6 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 50.72 214.52)" id="<Compound Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-576.5, -632.56)" d="M 515.9 632.9 Q 515.6999999999999 632.8 515.6999999999999 632.6 Q 515.6999999999999 632.4 515.9 632.3000000000001 L 576.3 597.4000000000001 Q 576.5 597.3000000000001 576.6999999999999 597.4000000000001 L 637.0999999999999 632.3000000000001 Q 637.3 632.4000000000001 637.3 632.6 Q 637.3 632.8000000000001 637.0999999999999 632.9 L 576.6999999999999 667.8 Q 576.5999999999999 667.8 576.4999999999999 667.8 Q 576.3999999999999 667.8 576.2999999999998 667.8 z M 516.9 632.6 L 576.5 667 L 636.2 632.6 L 576.5 598.1 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 50.72 253.15)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-576.5, -671.2)" d="M 576.1 667.1 L 576.1 675.3000000000001 L 576.9 675.3000000000001 L 576.9 667.1 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 50.35 214.55)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(180,224,224); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-576.13, -632.6)" d="M 610.6 628.9 L 610.2 628.3 C 609.5 626.5999999999999 606.8000000000001 625.4 604.4000000000001 625.6999999999999 L 581.3000000000001 628.4 L 581.6 612.1 C 581.6 610.4 579.6 608.8000000000001 577 608.5 L 576 608.4 C 573.5 608.1 571.4 609.3 571.4 611 L 571.1 629.6 L 544.6 632.6 C 542.2 632.9 540.9 634.5 541.7 636.2 L 542 636.9000000000001 C 542.8 638.6000000000001 545.4 639.7 547.8 639.5000000000001 L 570.9 636.8000000000001 L 570.6 653.1 C 570.6 654.8000000000001 572.7 656.4 575.2 656.7 L 576.2 656.8000000000001 C 578.7 657.1 580.8000000000001 655.9000000000001 580.8000000000001 654.2 L 581.2 635.6 L 607.6 632.5 C 610 632.3 611.3000000000001 630.6 610.6 628.9 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 50.34 214.53)" id="<Compound Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-576.12, -632.57)" d="M 576.9 657.3 Q 576.5 657.3 576.1 657.3 L 575.1 657.1999999999999 C 572.3000000000001 656.8 570.1 654.9999999999999 570.1 653.0999999999999 L 570.4 637.3 L 547.9 640 C 545.1999999999999 640.3 542.4 639 541.5 637.1 L 541.2 636.4 C 540.8000000000001 635.6 540.9000000000001 634.6999999999999 541.3000000000001 634 C 541.8000000000001 633 543.0000000000001 632.3 544.6 632.1 L 570.6 629.1 L 570.9 610.9 C 570.9 610.1999999999999 571.1999999999999 609.5 571.9 609 C 572.8 608.1 574.4 607.7 576.1 607.9 L 577.1 608 C 579.9 608.3 582.1 610.2 582.1 612.1 L 581.8000000000001 627.8000000000001 L 604.4000000000001 625.2 C 607.0000000000001 624.9000000000001 609.8000000000001 626.2 610.7 628.1 L 611 628.7 C 611.4 629.6 611.4 630.4000000000001 611 631.2 C 610.4 632.2 609.2 632.9000000000001 607.7 633 L 581.7 636 L 581.3000000000001 654.2 C 581.3000000000001 655 581.0000000000001 655.7 580.4000000000001 656.2 C 579.6000000000001 656.9000000000001 578.3000000000001 657.3000000000001 576.9000000000001 657.3000000000001 z M 571.4 636.1999999999999 L 571.1 653.0999999999999 C 571.1 654.4999999999999 573 655.8999999999999 575.2 656.1999999999999 L 576.3000000000001 656.3 C 577.7 656.4 579.0000000000001 656.0999999999999 579.7 655.4 C 580 655.1999999999999 580.3000000000001 654.8 580.3000000000001 654.1999999999999 L 580.7 635.0999999999999 L 607.6 631.9999999999999 C 608.7 631.8999999999999 609.7 631.3999999999999 610.1 630.6999999999999 Q 610.5 629.9999999999999 610.1 629.1999999999999 L 609.8000000000001 628.4999999999999 C 609.1 626.9999999999999 606.7 625.8999999999999 604.5000000000001 626.1999999999999 L 580.8000000000001 628.9 L 581.1 612.1 C 581.1 610.7 579.2 609.2 577 609 L 576 608.9 C 574.6 608.6999999999999 573.3 609 572.5 609.6999999999999 C 572.2 609.9999999999999 571.9 610.4 571.9 610.9999999999999 L 571.5 629.9999999999999 L 544.7 633.0999999999999 C 543.5 633.3 542.5 633.8 542.2 634.4999999999999 Q 541.8000000000001 635.1999999999999 542.1 635.9999999999999 L 542.4 636.6999999999999 C 543.1 638.1999999999999 545.6 639.1999999999999 547.6999999999999 638.9999999999999 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -201.45 189.55)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(180,224,224); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-324.33, -607.6)" d="M 318.1 598.7 C 319.5 597.7 323.40000000000003 600.8000000000001 326.90000000000003 605.8000000000001 C 330.40000000000003 610.7 332.00000000000006 615.5000000000001 330.6 616.5000000000001 C 329.20000000000005 617.5000000000001 325.20000000000005 614.3000000000001 321.8 609.4000000000001 C 318.3 604.5000000000001 316.6 599.7 318.1 598.7 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -201.43 189.55)" id="<Compound Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-324.35, -607.59)" d="M 330 617.3 C 327.7 617.3 324.1 613.8 321.3 609.6999999999999 C 319.6 607.3 318.2 604.8 317.5 602.6999999999999 C 316.7 600.4 316.8 598.8 317.7 598.1999999999999 C 318.7 597.4999999999999 320.2 597.9999999999999 322.09999999999997 599.4999999999999 C 323.79999999999995 600.8999999999999 325.7 602.9999999999999 327.4 605.3999999999999 C 330.7 610.0999999999999 333 615.5999999999999 331 616.9999999999999 Q 330.5 617.2999999999998 330 617.2999999999998 z M 318.7 599.0999999999999 Q 318.5 599.0999999999999 318.4 599.0999999999999 C 318.2 599.3 317.9 600.1999999999999 318.7 602.3999999999999 C 319.3 604.2999999999998 320.59999999999997 606.6999999999998 322.3 608.9999999999999 C 325.90000000000003 614.1999999999999 329.40000000000003 616.5999999999999 330.3 615.9999999999999 C 331.1 615.3999999999999 330 611.2999999999998 326.40000000000003 606.0999999999999 C 324.8 603.8 323.00000000000006 601.8 321.3 600.4999999999999 C 320 599.3999999999999 319.1 599.0999999999999 318.7 599.0999999999999 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -115.52 128.67)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-410.26, -546.72)" d="M 489.9 477.6 L 502.2 492.6 C 502.59999999999997 493.20000000000005 502.5 494 501.9 494.40000000000003 L 330.9 616.3000000000001 L 330.9 616.2 Q 330.9 616.3000000000001 330.9 616.3000000000001 C 328.7 604.1 320.79999999999995 600.0000000000001 318.2 598.9000000000001 C 318.09999999999997 598.9000000000001 318 598.7 318.2 598.6000000000001 C 325.8 593.2000000000002 475.7 486.40000000000015 488.4 477.3000000000001 C 488.9 477.0000000000001 489.5 477.10000000000014 489.9 477.60000000000014 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -115.52 128.72)" id="<Compound Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-410.26, -546.77)" d="M 330.9 617 L 330.7 616.9 L 330.4 617 L 330.29999999999995 616.4 C 328.19999999999993 604.5 320.29999999999995 600.4 317.99999999999994 599.5 C 317.69999999999993 599.4 317.49999999999994 599.1 317.49999999999994 598.8 C 317.49999999999994 598.5 317.59999999999997 598.3 317.79999999999995 598.0999999999999 C 323.69999999999993 593.8999999999999 415.99999999999994 528.1999999999999 462.49999999999994 494.9999999999999 L 487.99999999999994 476.89999999999986 C 488.79999999999995 476.29999999999984 489.79999999999995 476.4999999999999 490.3999999999999 477.1999999999999 L 502.5999999999999 492.1999999999999 C 502.8999999999999 492.59999999999985 503.0999999999999 493.09999999999985 502.9999999999999 493.59999999999985 C 502.9999999999999 494.1999999999999 502.6999999999999 494.59999999999985 502.2999999999999 494.89999999999986 z M 319.09999999999997 598.7 C 321.99999999999994 600.1 328.99999999999994 604.4000000000001 331.2 615.3000000000001 L 501.5 494.00000000000006 Q 501.8 493.80000000000007 501.8 493.50000000000006 Q 501.8 493.20000000000005 501.6 493.00000000000006 L 489.40000000000003 477.90000000000003 C 489.20000000000005 477.70000000000005 488.90000000000003 477.70000000000005 488.70000000000005 477.8 L 463.20000000000005 496 C 417.80000000000007 528.4 328.80000000000007 591.7 319.1 598.7 z M 318.4 598.4000000000001 Q 318.4 598.4000000000001 318.4 598.4000000000001 Q 318.4 598.4000000000001 318.4 598.4000000000001 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -24.28 65.65)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(180,224,224); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-501.5, -483.7)" d="M 514.7 472 C 515.3000000000001 471.9 515.7 472.6 515.4000000000001 473.1 L 503.1000000000001 493.1 Q 502.2000000000001 494.8 500.50000000000006 495.40000000000003 C 497.20000000000005 483.1 489.90000000000003 479.6 487.50000000000006 478.70000000000005 C 488.00000000000006 477.50000000000006 488.90000000000003 476.80000000000007 490.1000000000001 476.50000000000006 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -24.42 65.74)" id="<Compound Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-501.36, -483.79)" d="M 500.1 496.1 L 499.90000000000003 495.5 C 496.6 483.4 489.40000000000003 480 487.3 479.3 L 486.6 479.1 L 486.90000000000003 478.5 C 487.50000000000006 477.2 488.6 476.2 489.90000000000003 475.9 L 514.6 471.5 C 515.1 471.4 515.6 471.6 515.9 472 C 516.1999999999999 472.5 516.1999999999999 473 515.9 473.5 L 503.59999999999997 493.4 C 502.9 494.7 501.9 495.5 500.7 495.9 z M 488.3 478.40000000000003 C 491.1 479.6 497.6 483.40000000000003 500.90000000000003 494.6 Q 501.90000000000003 494 502.6 492.8 L 514.9 472.8 Q 514.9 472.8 514.9 472.7 Q 514.9 472.59999999999997 514.8 472.59999999999997 L 490.19999999999993 477.09999999999997 C 489.3999999999999 477.29999999999995 488.69999999999993 477.7 488.29999999999995 478.4 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -16.93 59.7)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-508.85, -477.75)" d="M 507.6 474.8 L 507.1 474.6 L 506.6 475.6 L 507.1 475.8 C 508.8 476.7 509.90000000000003 478.5 510 480.3 L 510 480.90000000000003 L 511.1 480.90000000000003 L 511.1 480.3 C 511 478 509.6 475.8 507.6 474.8 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -187.68 182.9)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-338.1, -600.95)" d="M 333.4 594 L 332.9 593.8 L 332.5 594.8 L 333 595 C 338.1 597.1 341.8 602.1 342.5 607.5 L 342.6 608.1 L 343.70000000000005 607.9 L 343.6 607.4 C 342.90000000000003 601.5 338.90000000000003 596.3 333.40000000000003 594 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -147.33 125.11)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-378.45, -543.15)" d="M 384.7 545.7 C 382 554.1 375 558.5 372 557.6 C 368.9 556.6 370.9 550.6 373.6 542.2 C 376.3 533.8000000000001 380.90000000000003 527.7 384 528.7 C 387 529.6 387.3 537.3000000000001 384.7 545.7 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -147.3 125.09)" id="<Compound Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-378.48, -543.14)" d="M 372.9 558.4 Q 372.29999999999995 558.4 371.79999999999995 558.1999999999999 C 369.49999999999994 557.4999999999999 369.59999999999997 554.6999999999999 369.99999999999994 552.8 C 370.3999999999999 550.0999999999999 371.49999999999994 546.5999999999999 372.79999999999995 542.5 L 372.99999999999994 542 C 375.8999999999999 532.9 380.69999999999993 526.9 384.19999999999993 528 C 387.69999999999993 529.2 388.19999999999993 536.8 385.29999999999995 545.9 C 382.79999999999995 553.8 376.69999999999993 558.4 372.9 558.4 z M 383.29999999999995 529.1999999999999 C 380.9 529.1999999999999 376.79999999999995 534.3 374.19999999999993 542.4 L 374.0999999999999 542.9 C 372.7999999999999 547 371.69999999999993 550.4 371.2999999999999 553 C 370.7999999999999 556.1 371.5999999999999 556.7 372.1999999999999 556.9 C 374.7999999999999 557.8 381.39999999999986 553.6 383.9999999999999 545.5 C 386.7999999999999 536.9 386.0999999999999 530 383.7999999999999 529.3 Q 383.5999999999999 529.1999999999999 383.2999999999999 529.1999999999999 z M 384.69999999999993 545.6999999999999 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -137.71 114.48)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(240,201,59); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-388.07, -532.53)" d="M 374 541.6 Q 375 538 376.5 534.1 C 381.4 521.6 386.1 513.1 395.3 515.3000000000001 C 406.90000000000003 518.2 401.3 529.3000000000001 396.40000000000003 541.8000000000001 C 395.20000000000005 544.9000000000001 393.8 547.6 392.3 550.1 C 385.2 550 378.6 546.6 374 541.6 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -137.73 114.51)" id="<Compound Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-388.05, -532.56)" d="M 392.3 550.8 Q 392.3 550.8 392.3 550.8 C 385.40000000000003 550.6999999999999 378.5 547.5 373.5 542 C 373.3 541.9 373.3 541.6 373.3 541.4 C 374 539 374.90000000000003 536.4 375.90000000000003 533.8 C 380.40000000000003 522.3 385.3 512.1999999999999 395.40000000000003 514.6999999999999 C 398.70000000000005 515.4999999999999 400.8 516.9 401.90000000000003 519.0999999999999 C 404.3 523.8 401.50000000000006 530.8 398.20000000000005 538.8999999999999 Q 397.6 540.4999999999999 397.00000000000006 542.0999999999999 C 395.80000000000007 544.9999999999999 394.50000000000006 547.8999999999999 392.90000000000003 550.3999999999999 C 392.70000000000005 550.5999999999999 392.50000000000006 550.7999999999998 392.3 550.7999999999998 z M 374.7 541.4 C 379.4 546.3 385.59999999999997 549.1999999999999 391.9 549.4 C 393.4 547 394.7 544.3 395.79999999999995 541.6 Q 396.4 540 396.99999999999994 538.4 C 399.99999999999994 530.9 402.79999999999995 523.9 400.69999999999993 519.6999999999999 C 399.79999999999995 517.9 397.8999999999999 516.6999999999999 395.0999999999999 515.9999999999999 C 386.8999999999999 513.8999999999999 382.2999999999999 521.1999999999999 377.0999999999999 534.2999999999998 Q 375.69999999999993 537.9999999999999 374.69999999999993 541.3999999999999 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -117.37 272.55)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(180,224,224); fill-rule: nonzero; opacity: 0.5;" vector-effect="non-scaling-stroke"  transform=" translate(-408.41, -690.6)" d="M 437.9 707.6 C 421.59999999999997 717 395.2 717 379 707.6 C 362.7 698.2 362.7 683 379 673.6 C 395.2 664.2 421.6 664.2 437.9 673.6 C 454.09999999999997 683 454.09999999999997 698.2 437.9 707.6 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -96.48 276.94)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-429.3, -694.99)" d="M 419.6 692.8 C 418.90000000000003 694.9 418.6 697 418.6 699.1999999999999 C 418.70000000000005 700.5999999999999 419.70000000000005 701.8 421.1 702.0999999999999 C 424.3 702.8 427.6 700.6999999999999 430.40000000000003 699.1999999999999 C 432.1 698.4 433.90000000000003 698.1999999999999 435.6 697.3 C 437.6 696 440 693.9 440 691.1999999999999 C 440 689.8 439.2 688.4 437.9 687.9 C 436.7 687.5 435.4 687.9 434.2 688.3 C 429.5 689.8 424.4 691.3 419.59999999999997 692.8 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -136 260.67)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-389.78, -678.71)" d="M 379.2 681.4 Q 379.09999999999997 682 379.09999999999997 682.6 C 379.09999999999997 683.7 379.59999999999997 684.8000000000001 380.49999999999994 685.3000000000001 C 382.29999999999995 686.3000000000001 385.29999999999995 685.2 386.8999999999999 684.2 C 388.5999999999999 683 390.3999999999999 682.6 392.3999999999999 682.1 C 394.0999999999999 681.6 395.7999999999999 681.1 397.2999999999999 680.1 C 398.6999999999999 679.1 399.9999999999999 677.6 400.3999999999999 675.8000000000001 C 400.69999999999993 674.3000000000001 399.8999999999999 672.5000000000001 398.2999999999999 671.9000000000001 C 397.0999999999999 671.5000000000001 395.8999999999999 671.9000000000001 394.6999999999999 672.3000000000001 C 391.09999999999985 673.5000000000001 387.39999999999986 674.6 383.6999999999999 675.8000000000001 C 381.1999999999999 676.6 379.4999999999999 678.8000000000001 379.1999999999999 681.4000000000001 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -132.06 193.45)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-393.72, -611.5)" d="M 379.4 646.7 C 380.09999999999997 637.4000000000001 382.29999999999995 632.3000000000001 383.4 626.3000000000001 C 383.7 624.2 384 621.9000000000001 383.9 619.4000000000001 C 383.9 617.4000000000001 383.7 615.3000000000001 383.4 612.9000000000001 C 380.29999999999995 592.6000000000001 380.7 571.7 386.09999999999997 557.6000000000001 C 389.79999999999995 555.3000000000002 394.09999999999997 554.3000000000002 398.2 555.3000000000002 C 402.2 556.3000000000002 405.7 559.4000000000002 407.2 563.7000000000002 C 408.7 567.8000000000002 408.2 572.6000000000001 407.59999999999997 577.2000000000002 C 405.59999999999997 593.7000000000002 402.7 606.2000000000002 399.2 622.4000000000002 Q 399.09999999999997 622.7000000000002 399.09999999999997 623.0000000000002 Q 399.09999999999997 623.0000000000002 399.09999999999997 623.0000000000002 Q 398.7 624.5000000000002 398.4 626.0000000000002 C 396.9 633.5000000000002 395.59999999999997 640.9000000000002 394.2 648.4000000000002 C 393.09999999999997 653.9000000000002 391.9 659.4000000000002 390.59999999999997 665.0000000000002 Q 390.4 665.8000000000002 390.2 666.7000000000003 C 387.2 668.4000000000003 383.2 668.6000000000003 380.3 666.9000000000003 C 379.8 660.2000000000003 378.90000000000003 653.5000000000003 379.40000000000003 646.7000000000003 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -140.53 254.97)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-385.25, -673.01)" d="M 390.2 666.7 Q 389.4 670.2 388.59999999999997 673.6 C 388.4 674.7 387.2 677 386.49999999999994 677.8000000000001 C 385.59999999999997 678.6 383.59999999999997 679.5000000000001 382.19999999999993 679.3000000000001 C 380.29999999999995 679.0000000000001 380.5999999999999 676.1 380.5999999999999 673.9000000000001 Q 380.5999999999999 672.6000000000001 380.4999999999999 671.3000000000001 Q 380.39999999999986 669.1 380.2999999999999 666.9000000000001 C 383.1999999999999 668.6000000000001 387.1999999999999 668.4000000000001 390.1999999999999 666.7 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -104.62 271.88)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-421.16, -689.92)" d="M 425.8 683.4 Q 425.90000000000003 686.1999999999999 426.1 689 C 426.3 690.9 427 693.4 426 695.1 C 424.4 697.6 421.2 696.2 420 694.3000000000001 C 419.2 693.1 418.7 691.8000000000001 418.2 690.6 Q 417 687.8000000000001 415.8 684.9 C 419.40000000000003 685.5 422.6 685.4 425.8 683.4 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -114.98 208.71)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-410.8, -626.76)" d="M 403.2 568.3 C 407.59999999999997 568.1999999999999 412.09999999999997 570.4 414.8 574.3 C 417.40000000000003 578.0999999999999 418.3 583.0999999999999 419 588 C 420.8 599.7 421.6 609.7 422.2 620.3 Q 422.4 623.5 422.59999999999997 626.8 Q 422.9 631.4 423.2 636.4 C 423.8 646.5 424.3 656.5 424.8 666.6 Q 425.2 674.2 425.7 681.7 Q 425.7 682.6 425.8 683.4000000000001 C 422.6 685.4000000000001 419.40000000000003 685.5000000000001 415.8 684.9000000000001 C 413.40000000000003 679.0000000000001 411.1 672.9000000000001 409.90000000000003 666.8000000000001 C 406.8 651.2 410.8 645.6 404.40000000000003 631.2 Q 403.20000000000005 628.6 402.1 626 Q 400.70000000000005 622.5 399.40000000000003 619 Q 397.40000000000003 613.4 395.8 607.9 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -132.5 199.05)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-393.28, -617.1)" d="M 382.6 680 Q 382.40000000000003 680 382.1 679.9 C 379.90000000000003 679.6 379.90000000000003 676.9 379.90000000000003 674.6999999999999 Q 379.90000000000003 674.3 379.90000000000003 673.9 Q 379.90000000000003 672.6 379.90000000000003 671.3 C 379.70000000000005 668.4 379.50000000000006 665.5 379.3 662.6999999999999 C 378.8 657.4 378.3 651.9999999999999 378.7 646.5999999999999 C 379.2 640.5999999999999 380.3 636.1999999999999 381.3 632.3999999999999 C 382.8 626.5999999999999 384 621.6999999999998 382.7 612.9999999999999 C 379.4 591.0999999999999 380.4 570.7999999999998 385.4 557.3999999999999 Q 385.5 557.1999999999998 385.7 557.0999999999999 C 389.9 554.4999999999999 394.3 553.5999999999999 398.3 554.5999999999999 C 402.7 555.8 406.3 559.0999999999999 407.8 563.3999999999999 L 408 564.0999999999999 L 406.8 564.4999999999999 L 406.6 563.8999999999999 C 405.20000000000005 559.9999999999999 401.90000000000003 556.8999999999999 398 555.8999999999999 C 394.4 554.9999999999999 390.4 555.7999999999998 386.6 558.0999999999999 C 381.70000000000005 571.3 380.70000000000005 591.1999999999999 384 612.8 C 385.4 621.8 384.1 626.8 382.6 632.6999999999999 C 381.6 636.4999999999999 380.5 640.8 380.1 646.6999999999999 C 379.70000000000005 651.9999999999999 380.1 657.4 380.6 662.5999999999999 C 380.8 665.3999999999999 381.1 668.3 381.20000000000005 671.1999999999999 Q 381.30000000000007 672.5999999999999 381.30000000000007 673.9 Q 381.30000000000007 674.3 381.30000000000007 674.6999999999999 C 381.20000000000005 676.9 381.30000000000007 678.4999999999999 382.30000000000007 678.5999999999999 C 383.50000000000006 678.8 385.30000000000007 677.9999999999999 386.00000000000006 677.3 C 386.70000000000005 676.5999999999999 387.80000000000007 674.4 388.00000000000006 673.5 Q 388.90000000000003 669.2 389.90000000000003 664.8 L 390.1 663.9 C 391.3 658.8 392.5 653.5 393.5 648.1999999999999 C 394 645.9 394.5 643.3 395 640.6999999999999 C 396.3 633.6999999999999 397.6 626.4 399 620.0999999999999 L 399.1 619.3999999999999 L 400.40000000000003 619.6999999999998 L 400.3 620.3999999999999 C 399 626.5999999999999 397.6 633.8999999999999 396.3 640.9999999999999 C 395.8 643.5999999999999 395.3 646.0999999999999 394.90000000000003 648.4999999999999 C 393.90000000000003 653.7999999999998 392.6 659.0999999999999 391.40000000000003 664.1999999999999 L 391.20000000000005 665.0999999999999 Q 390.20000000000005 669.3999999999999 389.30000000000007 673.8 C 389.00000000000006 674.9 387.80000000000007 677.3 386.9000000000001 678.1999999999999 C 386.1000000000001 679.0999999999999 384.2000000000001 679.9999999999999 382.6000000000001 679.9999999999999 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -114.69 214.35)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-411.09, -632.4)" d="M 423.6 697.1 C 422 697.1 420.3 696.1 419.40000000000003 694.6 C 418.6 693.5 418.1 692.1 417.50000000000006 690.8000000000001 L 416.40000000000003 687.9000000000001 C 413.50000000000006 681.1000000000001 410.6 674.0000000000001 409.20000000000005 666.9000000000001 C 408.00000000000006 660.6000000000001 407.90000000000003 655.9000000000001 407.80000000000007 651.7 C 407.70000000000005 645.4000000000001 407.6000000000001 640 403.80000000000007 631.5 C 400.20000000000005 623.7 397.30000000000007 615.8 395.20000000000005 608 L 395.00000000000006 607.4 L 396.30000000000007 607 L 396.4000000000001 607.7 C 398.6000000000001 615.3000000000001 401.5000000000001 623.2 405.0000000000001 631 C 408.9000000000001 639.7 409.0000000000001 645.2 409.10000000000014 651.6 C 409.20000000000016 655.8000000000001 409.3000000000001 660.5 410.5000000000001 666.7 C 411.9000000000001 673.6 414.8000000000001 680.6 417.60000000000014 687.4000000000001 L 418.8000000000001 690.3000000000001 C 419.3000000000001 691.6 419.8000000000001 692.8000000000001 420.5000000000001 693.9000000000001 C 421.2000000000001 695.0000000000001 422.5000000000001 695.8000000000001 423.60000000000014 695.8000000000001 Q 423.60000000000014 695.8000000000001 423.60000000000014 695.8000000000001 Q 424.8000000000001 695.8000000000001 425.40000000000015 694.8000000000001 C 426.00000000000017 693.7 425.8000000000001 692.0000000000001 425.60000000000014 690.5000000000001 Q 425.5000000000001 689.8000000000001 425.5000000000001 689.1000000000001 Q 425.2000000000001 685.4000000000001 425.0000000000001 681.8000000000002 Q 424.5000000000001 674.2000000000002 424.10000000000014 666.7000000000002 Q 423.90000000000015 661.9000000000002 423.60000000000014 657.1000000000001 Q 423.10000000000014 646.8000000000002 422.5000000000001 636.5000000000001 L 422.10000000000014 630.1000000000001 C 421.3000000000001 615.7000000000002 420.60000000000014 603.3000000000002 418.40000000000015 588.1000000000001 C 417.60000000000014 583.1000000000001 416.70000000000016 578.3000000000002 414.20000000000016 574.7000000000002 C 411.70000000000016 571.1000000000001 407.50000000000017 568.9000000000002 403.20000000000016 569.0000000000001 L 402.60000000000014 569.0000000000001 L 402.5000000000001 567.7000000000002 L 403.2000000000001 567.7000000000002 C 407.9000000000001 567.6000000000001 412.6000000000001 570.0000000000001 415.3000000000001 573.9000000000002 C 418.0000000000001 577.8000000000002 418.90000000000015 582.7000000000002 419.7000000000001 587.9000000000002 C 422.0000000000001 603.1000000000003 422.7000000000001 615.6000000000003 423.5000000000001 630.0000000000002 L 423.9000000000001 636.4000000000002 Q 424.5000000000001 646.7000000000002 425.0000000000001 657.0000000000002 Q 425.2000000000001 661.8000000000002 425.5000000000001 666.6000000000003 Q 425.9000000000001 674.1000000000003 426.3000000000001 681.7000000000003 Q 426.60000000000014 685.3000000000003 426.8000000000001 689.0000000000002 Q 426.90000000000015 689.6000000000003 427.0000000000001 690.3000000000002 C 427.2000000000001 692.0000000000002 427.4000000000001 694.0000000000002 426.5000000000001 695.5000000000002 C 425.9000000000001 696.5000000000002 424.9000000000001 697.1000000000003 423.60000000000014 697.1000000000003 Q 423.60000000000014 697.1000000000003 423.60000000000014 697.1000000000003 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -125.8 142.98)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(240,201,59); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-399.98, -561.03)" d="M 378.1 579.7 C 384.3 557.8000000000001 379.3 534.3000000000001 387.40000000000003 523 C 395.6 511.6 419.40000000000003 534.9 419.40000000000003 534.9 C 419.40000000000003 534.9 420.3 557.6999999999999 422.20000000000005 579.4 C 423.20000000000005 591.4 414.70000000000005 602.1999999999999 403.90000000000003 602.1999999999999 C 395.90000000000003 602.1999999999999 374.40000000000003 602.0999999999999 378.1 579.6999999999999 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -125.81 150.5)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-399.96, -568.55)" d="M 403.7 602.9 C 398.5 602.9 386.7 602.8 380.7 595.8 C 377.4 591.9 376.3 586.5 377.5 579.5999999999999 C 379.7 571.5999999999999 380.1 564.1999999999999 380.6 556.8999999999999 C 380.90000000000003 551.6999999999998 381.20000000000005 546.1999999999998 382.20000000000005 540.6999999999998 L 382.30000000000007 539.9999999999998 L 383.70000000000005 540.2999999999997 L 383.50000000000006 540.8999999999997 C 382.50000000000006 546.2999999999997 382.20000000000005 551.4999999999998 381.90000000000003 556.9999999999998 C 381.50000000000006 564.2999999999997 381.00000000000006 571.7999999999997 378.8 579.8999999999997 C 377.7 586.2999999999997 378.7 591.2999999999997 381.7 594.8999999999997 C 387.4 601.5999999999998 399 601.5999999999998 403.9 601.5999999999998 C 408.4 601.5999999999998 412.7 599.5999999999998 416 595.9999999999998 C 420 591.5999999999998 422 585.5999999999998 421.5 579.3999999999997 C 419.7 557.9999999999998 418.8 535.1999999999997 418.8 534.8999999999997 L 418.7 534.2999999999997 L 420.09999999999997 534.1999999999997 L 420.09999999999997 534.8999999999997 C 420.09999999999997 535.0999999999998 420.99999999999994 557.8999999999997 422.79999999999995 579.2999999999997 C 423.4 585.7999999999997 421.29999999999995 592.1999999999997 416.99999999999994 596.8999999999997 C 413.49999999999994 600.7999999999997 408.79999999999995 602.8999999999997 403.8999999999999 602.8999999999997 Q 403.7999999999999 602.8999999999997 403.69999999999993 602.8999999999997 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -85.87 120.89)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-439.91, -538.94)" d="M 441.5 543.3 C 440.3 544.5999999999999 442.6 544.6999999999999 441.4 545.3 C 439.59999999999997 543.0999999999999 437.09999999999997 541 433.7 539.5999999999999 C 425.59999999999997 535.9999999999999 418 535.4999999999999 416.7 538.3999999999999 C 415.4 541.3999999999999 421 546.5999999999999 429.09999999999997 550.1999999999998 C 433.09999999999997 551.8999999999999 436.4 553.3999999999999 438.99999999999994 554.1999999999998 Q 439.09999999999997 554.1999999999998 439.29999999999995 554.2999999999998 C 445.59999999999997 556.6999999999998 449.99999999999994 551.4999999999999 456.09999999999997 540.6999999999998 C 462.79999999999995 528.9999999999998 463.29999999999995 522.9999999999998 463.29999999999995 522.9999999999998 C 463.29999999999995 522.9999999999998 457.59999999999997 526.1999999999998 451.4 531.5999999999998 C 448.09999999999997 534.4999999999998 444.5 540.0999999999998 441.5 543.2999999999998 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -85.79 120.7)" id="<Compound Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-439.99, -538.75)" d="M 442.2 555.4 Q 440.7 555.4 439.09999999999997 554.6999999999999 Q 438.9 554.6999999999999 438.79999999999995 554.5999999999999 C 436.4 553.8999999999999 433.09999999999997 552.4999999999999 429.29999999999995 550.8 L 428.9 550.5999999999999 C 420.4 546.8999999999999 414.9 541.4999999999999 416.29999999999995 538.1999999999999 C 417.69999999999993 534.9999999999999 425.49999999999994 535.4 433.9 539.0999999999999 C 436.5 540.1999999999999 438.79999999999995 541.8 440.7 543.6999999999999 Q 440.8 543.3 441.2 542.9 C 442.4 541.6 443.7 540 445 538.1999999999999 C 447 535.5999999999999 449.1 532.9 451.1 531.1999999999999 C 457.20000000000005 525.9 463 522.5999999999999 463.1 522.4999999999999 L 463.90000000000003 522.0999999999999 L 463.8 522.9999999999999 C 463.8 523.2999999999998 463.2 529.1999999999999 456.5 540.9999999999999 C 451.4 550.0999999999999 447.3 555.3999999999999 442.2 555.3999999999999 z M 421.59999999999997 537 C 419.2 537 417.59999999999997 537.6 417.2 538.6 C 416.2 541 420.8 546 429.3 549.7 L 429.7 549.9000000000001 C 433.5 551.6000000000001 436.7 553.0000000000001 439.2 553.7 C 439.3 553.8000000000001 439.4 553.8000000000001 439.4 553.8000000000001 C 445.5 556.1 449.59999999999997 551.1 455.7 540.5000000000001 C 460.59999999999997 531.7000000000002 462.2 526.1000000000001 462.7 524.0000000000001 C 460.8 525.1000000000001 456.3 527.9000000000001 451.7 532.0000000000001 C 449.8 533.6000000000001 447.8 536.2000000000002 445.8 538.8000000000001 C 444.40000000000003 540.6 443.1 542.3000000000001 441.90000000000003 543.6 C 441.70000000000005 543.8000000000001 441.6 543.9 441.6 544 C 441.6 544 441.8 544.2 441.90000000000003 544.2 C 442.00000000000006 544.4000000000001 442.3 544.7 442.20000000000005 545 C 442.20000000000005 545.4 441.90000000000003 545.6 441.70000000000005 545.7 L 441.30000000000007 545.9000000000001 L 441.00000000000006 545.6000000000001 C 439.70000000000005 544.0000000000001 437.30000000000007 541.7000000000002 433.50000000000006 540.0000000000001 C 428.80000000000007 537.9000000000001 424.6000000000001 537.0000000000001 421.6000000000001 537.0000000000001 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -108.35 120.39)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(240,201,59); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-417.43, -538.43)" d="M 399.1 524.5 C 404.5 513.8 416.3 523.7 429.8 533.3 Q 433.6 536 436.8 538.9 C 436 546.1999999999999 432.6 552.1999999999999 427.7 557 C 424.4 555.3 420.9 553.2 417.4 550.7 C 403.9 541 394.9 533 399.09999999999997 524.5 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -98.86 124.05)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-426.92, -542.1)" d="M 428 557.6 C 427.8 557.6 427.6 557.6 427.4 557.5 C 423.9 555.8 420.4 553.7 417 551.2 L 416.4 550.8000000000001 L 417.2 549.7 L 417.8 550.1 C 421 552.4 424.3 554.4 427.6 556.1 C 432.5 551.2 435.3 545.5 436.1 539.2 C 433.8 537.3000000000001 425.70000000000005 531.5 422.20000000000005 529 L 420.30000000000007 527.7 L 421.1000000000001 526.6 L 423.00000000000006 527.9 C 427.00000000000006 530.8 435.30000000000007 536.6999999999999 437.20000000000005 538.4 C 437.40000000000003 538.6 437.50000000000006 538.8 437.40000000000003 539 C 436.70000000000005 545.9 433.6 552.2 428.20000000000005 557.4 Q 428.1 557.5 428.00000000000006 557.6 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -127.68 98.11)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-398.1, -516.15)" d="M 403.7 517 L 403.09999999999997 511.4 L 393.09999999999997 511 L 392.9 516 C 391.4 516.5 390.09999999999997 517.2 389.2 518.1 C 389.59999999999997 519.7 391.8 521.2 395.09999999999997 521.3000000000001 C 398.2 521.4000000000001 405.2 520.3000000000001 406.99999999999994 519.5000000000001 C 406.09999999999997 518.5000000000001 404.99999999999994 517.7000000000002 403.69999999999993 517.0000000000001 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -127.71 98.1)" id="<Compound Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-398.07, -516.15)" d="M 395.6 522 Q 395.3 522 395.1 522 C 391.70000000000005 521.8 389 520.3 388.5 518.3 C 388.5 518.0999999999999 388.5 517.8 388.7 517.6999999999999 C 389.7 516.6999999999999 391.09999999999997 515.9999999999999 392.2 515.5999999999999 L 392.4 510.8999999999999 C 392.4 510.5999999999999 392.7 510.2999999999999 393.09999999999997 510.2999999999999 L 403.09999999999997 510.6999999999999 C 403.49999999999994 510.6999999999999 403.7 510.9999999999999 403.79999999999995 511.2999999999999 L 404.4 516.5999999999999 C 405.59999999999997 517.3 406.7 518.0999999999999 407.5 518.9999999999999 C 407.6 519.1999999999999 407.7 519.3999999999999 407.6 519.5999999999999 C 407.6 519.8 407.5 519.9999999999999 407.3 520.0999999999999 C 405.40000000000003 520.8999999999999 399 521.9999999999999 395.6 521.9999999999999 z M 390 518.3 C 390.5 519.4 392.4 520.5 395.1 520.5999999999999 C 397.8 520.6999999999999 403.20000000000005 519.8999999999999 405.70000000000005 519.1999999999999 Q 404.70000000000005 518.3 403.40000000000003 517.5999999999999 C 403.20000000000005 517.4999999999999 403.1 517.3 403.1 517.0999999999999 L 402.5 511.9999999999999 L 393.7 511.5999999999999 L 393.5 516.0999999999999 C 393.5 516.3 393.3 516.5999999999999 393.1 516.6999999999999 C 391.8 517.0999999999999 390.70000000000005 517.6999999999999 390 518.3 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -127.47 84.41)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-398.31, -502.46)" d="M 388.8 506.4 C 391.1 513 399.6 516.1 405 514.1999999999999 C 410.4 512.3 409 503.5999999999999 406.6 496.99999999999994 C 404.3 490.3999999999999 399.70000000000005 488.99999999999994 394.3 490.99999999999994 C 388.90000000000003 492.8999999999999 386.40000000000003 499.79999999999995 388.8 506.3999999999999 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -127.46 84.4)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-398.32, -502.45)" d="M 401.5 515.4 Q 401 515.4 400.5 515.4 L 399.8 515.4 L 399.90000000000003 514 L 400.6 514.1 C 402.1 514.2 403.6 514 404.8 513.6 C 406.90000000000003 512.8000000000001 408 510.70000000000005 408.1 507.40000000000003 C 408.1 504.6 407.40000000000003 501.00000000000006 406 497.3 C 404.9 494.3 403.4 492.3 401.3 491.40000000000003 Q 398.5 490.20000000000005 394.5 491.6 C 392.1 492.5 390.2 494.40000000000003 389.2 497.20000000000005 C 388.3 499.90000000000003 388.3 503.1 389.4 506.20000000000005 C 390.4 508.80000000000007 392.5 511.1 395.29999999999995 512.6 L 395.9 512.9 L 395.29999999999995 514.1 L 394.69999999999993 513.8000000000001 C 391.5999999999999 512.1 389.19999999999993 509.6000000000001 388.19999999999993 506.6000000000001 C 386.99999999999994 503.30000000000007 386.8999999999999 499.80000000000007 387.99999999999994 496.7000000000001 C 389.09999999999997 493.6000000000001 391.19999999999993 491.3000000000001 393.99999999999994 490.3000000000001 C 396.99999999999994 489.3000000000001 399.59999999999997 489.2000000000001 401.8999999999999 490.2000000000001 C 404.2999999999999 491.2000000000001 406.0999999999999 493.5000000000001 407.2999999999999 496.8000000000001 C 408.6999999999999 500.7000000000001 409.3999999999999 504.5000000000001 409.3999999999999 507.40000000000015 C 409.3999999999999 512.3000000000002 407.0999999999999 514.2000000000002 405.19999999999993 514.8000000000002 C 404.0999999999999 515.2000000000002 402.8999999999999 515.4000000000002 401.49999999999994 515.4000000000002 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -136.53 70.42)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-389.25, -488.47)" d="M 390.5 489.9 C 389.1 488.7 386.7 488.4 384.8 489.2 C 385.7 489.4 386.7 489.3 387.7 489.2 C 388.59999999999997 489.2 389.7 489.4 390.2 490 C 390.8 490.7 391.09999999999997 491.6 392.4 491.1 C 393.2 490.8 393.29999999999995 490.3 393.29999999999995 489.70000000000005 C 393.19999999999993 489.20000000000005 392.59999999999997 488.70000000000005 392.59999999999997 488.1 C 392.49999999999994 487.20000000000005 393.09999999999997 486.40000000000003 393.7 485.70000000000005 C 391.7 486.30000000000007 390.3 488.20000000000005 390.5 489.90000000000003 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -104.73 266.5)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-421.05, -684.55)" d="M 418.9 686.3 Q 417.7 686.3 416.4 686.0999999999999 L 415.7 685.9999999999999 L 415.9 684.5999999999999 L 416.59999999999997 684.6999999999999 C 419.9 685.1999999999999 422.09999999999997 684.8 425.2 683.0999999999999 L 425.8 682.8 L 426.40000000000003 684 L 425.90000000000003 684.3 C 423.3 685.5999999999999 421.3 686.3 418.90000000000003 686.3 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -140.48 249.45)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-385.3, -667.5)" d="M 384.8 668.9 C 383.3 668.9 381.8 668.6 380.40000000000003 668.1 L 379.8 667.9 L 380.3 666.6 L 380.90000000000003 666.8000000000001 C 383.70000000000005 667.9000000000001 386.90000000000003 667.7 389.6 666.4000000000001 L 390.20000000000005 666.1000000000001 L 390.80000000000007 667.3000000000002 L 390.20000000000005 667.6000000000001 C 388.50000000000006 668.5000000000001 386.6 668.9000000000001 384.80000000000007 668.9000000000001 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -128.75 83.57)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-397.03, -501.61)" d="M 386.6 508.8 C 387.8 512 390.20000000000005 515.1 393.6 516.1 C 396.90000000000003 517 401.1 515.2 401.8 511.8 C 402.2 509.90000000000003 401.40000000000003 507.90000000000003 401 506.1 C 400.5 504.20000000000005 400.3 502 401.4 500.5 C 402.79999999999995 498.7 405.5 498.5 407.09999999999997 496.9 C 408.99999999999994 495.09999999999997 408.79999999999995 491.9 407.2 489.9 C 405.59999999999997 487.79999999999995 402.9 486.9 400.4 486.9 C 394.5 486.79999999999995 390.29999999999995 488.7 387.7 494 C 385.4 498.5 384.9 504 386.59999999999997 508.8 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -126.13 86.65)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-399.65, -504.7)" d="M 397.6 504.7 C 397.6 506.4 398.5 507.8 399.6 507.8 C 400.70000000000005 507.8 401.70000000000005 506.40000000000003 401.70000000000005 504.7 C 401.70000000000005 503 400.70000000000005 501.59999999999997 399.6 501.59999999999997 C 398.5 501.59999999999997 397.6 502.99999999999994 397.6 504.7 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -165.03 -186.53)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(240,201,59); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-360.75, -231.52)" d="M 379.8 195.8 L 379.8 195.70000000000002 L 386.6 199.60000000000002 L 382.1 200.00000000000003 Q 382.20000000000005 200.60000000000002 382.20000000000005 201.30000000000004 L 382.20000000000005 227.10000000000005 C 382.20000000000005 232.70000000000005 378.30000000000007 239.40000000000006 373.50000000000006 242.20000000000005 L 341.1000000000001 267.90000000000003 L 334.9000000000001 264.40000000000003 L 334.9000000000001 218.60000000000002 L 373.5000000000001 196.3 C 376.0000000000001 194.9 378.2000000000001 194.8 379.8000000000001 195.8 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -165.01 -188.26)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-360.77, -229.79)" d="M 334.6 218 L 373.20000000000005 195.7 C 375.70000000000005 194.29999999999998 378.1 194.1 379.90000000000003 195.1 Q 380.00000000000006 195.2 380.1 195.2 L 386.90000000000003 199 C 387.20000000000005 199.1 387.3 199.4 387.20000000000005 199.7 C 387.20000000000005 199.89999999999998 387.00000000000006 200.1 386.70000000000005 200.2 L 382.80000000000007 200.5 Q 382.80000000000007 200.9 382.80000000000007 201.3 L 382.80000000000007 227.70000000000002 L 381.6000000000001 227.70000000000002 L 381.6000000000001 201.3 Q 381.6000000000001 200.70000000000002 381.50000000000006 200.10000000000002 Q 381.50000000000006 199.8 381.6000000000001 199.60000000000002 Q 381.80000000000007 199.40000000000003 382.00000000000006 199.40000000000003 L 384.6000000000001 199.10000000000002 L 379.7000000000001 196.40000000000003 Q 379.6000000000001 196.40000000000003 379.5000000000001 196.30000000000004 C 378.0000000000001 195.40000000000003 376.0000000000001 195.60000000000005 373.8000000000001 196.80000000000004 L 335.5000000000001 218.90000000000003 L 335.5000000000001 265.1 L 334.3000000000001 265.1 L 334.3000000000001 218.60000000000002 C 334.3000000000001 218.3 334.40000000000015 218.10000000000002 334.60000000000014 218.00000000000003 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -160.63 -184.65)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(240,201,59); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-365.15, -233.4)" d="M 388.8 204.9 L 388.8 230.70000000000002 C 388.8 236.3 384.90000000000003 243.00000000000003 380.1 245.8 L 341.5 268.1 L 341.5 222.20000000000002 L 380.1 199.9 C 384.90000000000003 197.1 388.8 199.4 388.8 204.9 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -160.73 -192.65)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-365.05, -225.4)" d="M 365 219.1 C 368 217.4 370.5 218.79999999999998 370.5 222.2 C 370.5 225.7 368 229.89999999999998 365 231.7 C 362 233.39999999999998 359.6 232 359.6 228.5 C 359.6 225.1 362 220.8 365 219.1 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -160.78 -179.3)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-365, -238.75)" d="M 365 233.1 C 367.1 231.9 369 229.7 370.2 227.29999999999998 C 373.5 227.99999999999997 375.7 231.79999999999998 375.7 237.7 Q 375.7 237.7 375.7 237.79999999999998 L 354.3 250.2 Q 354.3 250.1 354.3 250 C 354.3 244.2 356.6 237.8 359.90000000000003 233.2 C 361.1 234.2 363.00000000000006 234.29999999999998 365.00000000000006 233.1 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -160.73 -192.67)" id="<Compound Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-365.05, -225.37)" d="M 364.7 218.6 C 366.4 217.6 368 217.5 369.2 218.2 C 370.4 218.89999999999998 371.09999999999997 220.29999999999998 371.09999999999997 222.2 C 371.09999999999997 225.89999999999998 368.49999999999994 230.39999999999998 365.29999999999995 232.2 Q 363.9 233 362.59999999999997 233 Q 361.7 233 360.9 232.6 C 359.59999999999997 231.9 359 230.4 359 228.5 C 359 224.9 361.6 220.4 364.7 218.6 z M 361.5 231.5 C 362.3 232 363.5 231.9 364.7 231.1 C 367.59999999999997 229.5 369.9 225.5 369.9 222.2 C 369.9 220.79999999999998 369.4 219.7 368.59999999999997 219.2 Q 368.09999999999997 219 367.49999999999994 219 Q 366.49999999999994 219 365.29999999999995 219.6 C 362.49999999999994 221.29999999999998 360.19999999999993 225.29999999999998 360.19999999999993 228.5 C 360.19999999999993 230 360.69999999999993 231 361.49999999999994 231.5 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -160.73 -179.32)" id="<Compound Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-365.05, -238.73)" d="M 359.8 232.6 Q 360.1 232.6 360.3 232.7 C 361.3 233.6 363 233.5 364.7 232.5 C 366.59999999999997 231.4 368.4 229.4 369.59999999999997 227 C 369.79999999999995 226.7 369.99999999999994 226.6 370.29999999999995 226.7 C 373.99999999999994 227.5 376.4 231.7 376.4 237.79999999999998 C 376.4 237.99999999999997 376.2 238.2 376.09999999999997 238.29999999999998 L 354.59999999999997 250.7 Q 354.49999999999994 250.79999999999998 354.29999999999995 250.79999999999998 Q 354.19999999999993 250.79999999999998 353.99999999999994 250.7 C 353.79999999999995 250.6 353.69999999999993 250.39999999999998 353.69999999999993 250.2 C 353.69999999999993 250.2 353.69999999999993 250 353.69999999999993 250 C 353.69999999999993 244.3 355.8999999999999 237.7 359.3999999999999 232.8 Q 359.49999999999994 232.60000000000002 359.7999999999999 232.60000000000002 z M 375.1 237.4 C 375.1 232.5 373.3 228.9 370.5 228 C 369.2 230.4 367.3 232.4 365.3 233.6 C 363.40000000000003 234.7 361.5 234.9 360 234.1 C 357.1 238.29999999999998 355.2 243.9 355 249.1 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -163.58 -184.65)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-362.2, -233.4)" d="M 341.5 267.4 L 379.8 245.29999999999998 C 384.40000000000003 242.6 388.1 236.1 388.1 230.7 L 388.1 204.89999999999998 C 388.1 202.49999999999997 387.3 200.7 385.90000000000003 199.79999999999998 C 384.50000000000006 198.99999999999997 382.50000000000006 199.2 380.40000000000003 200.39999999999998 L 341.8 222.7 C 341.6 222.79999999999998 341.40000000000003 222.79999999999998 341.2 222.7 L 335.4 219.7 L 335.9 218.6 L 341.5 221.5 L 379.8 199.4 C 382.3 197.9 384.7 197.70000000000002 386.5 198.8 C 388.4 199.8 389.4 202 389.4 204.9 L 389.4 230.70000000000002 C 389.4 236.50000000000003 385.29999999999995 243.50000000000003 380.4 246.3 L 341.79999999999995 268.6 Q 341.69999999999993 268.70000000000005 341.49999999999994 268.70000000000005 Q 341.29999999999995 268.70000000000005 341.19999999999993 268.6 L 334.99999999999994 265.20000000000005 L 335.59999999999997 264.1 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -184.33 -173.7)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-341.45, -244.35)" d="M 342.1 221.6 L 342 267.1 L 340.8 267.1 L 340.90000000000003 221.60000000000002 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -150.68 37.1)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(240,201,59); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-375.1, -455.15)" d="M 379 465.5 L 379.1 449.7 L 371.1 444.8 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -144.28 38.45)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(240,201,59); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-381.5, -456.5)" d="M 379 465.5 L 384 462.9 L 384 447.5 L 379.1 449.7 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -144.28 38.45)" id="<Compound Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-381.5, -456.5)" d="M 378.5 466.3 L 378.6 449.40000000000003 L 384.5 446.70000000000005 L 384.5 463.20000000000005 z M 379.6 450.1 L 379.5 464.70000000000005 L 383.5 462.6 L 383.5 448.3 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -150.68 37.12)" id="<Compound Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-375.1, -455.17)" d="M 379.3 449.3 Q 379.6 449.5 379.6 449.7 L 379.40000000000003 465.5 C 379.40000000000003 465.7 379.3 465.9 379.00000000000006 465.9 Q 379.00000000000006 466 379.00000000000006 466 C 378.80000000000007 466 378.6000000000001 465.8 378.50000000000006 465.6 L 370.6000000000001 445 C 370.6000000000001 444.8 370.6000000000001 444.6 370.80000000000007 444.5 C 370.9000000000001 444.3 371.20000000000005 444.3 371.30000000000007 444.4 z M 378.6 450 L 372.1 446 L 378.5 462.9 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -144.33 38.42)" id="<Compound Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-381.45, -456.47)" d="M 384.2 447.1 Q 384.4 447.20000000000005 384.4 447.5 L 384.4 462.9 C 384.4 463.09999999999997 384.29999999999995 463.2 384.2 463.29999999999995 L 379.2 465.9 Q 379.09999999999997 466 379 466 Q 378.8 466 378.7 465.9 C 378.59999999999997 465.79999999999995 378.5 465.59999999999997 378.5 465.5 L 378.6 449.7 C 378.6 449.59999999999997 378.70000000000005 449.4 378.90000000000003 449.3 L 383.8 447 Q 384 446.9 384.2 447.1 z M 383.5 448.20000000000005 L 379.6 450.00000000000006 L 379.40000000000003 464.70000000000005 L 383.50000000000006 462.6 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -156.23 2)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(240,201,59); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-369.55, -420.05)" d="M 396.9 410.5 L 346.9 381.6 C 345.5 380.8 344.29999999999995 380.70000000000005 343.29999999999995 381.20000000000005 L 336.29999999999995 385.30000000000007 L 341.09999999999997 388.50000000000006 L 341.09999999999997 415.00000000000006 C 341.09999999999997 419.30000000000007 343.7 424.30000000000007 346.9 426.20000000000005 L 394 453.30000000000007 L 394.1 459.20000000000005 L 400.8 455.40000000000003 L 400.7 455.40000000000003 C 402 454.70000000000005 402.8 453.00000000000006 402.8 450.6 L 402.8 421.70000000000005 C 402.8 417.40000000000003 400.2 412.40000000000003 396.90000000000003 410.50000000000006 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -156.48 2.2)" id="<Compound Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-369.3, -420.25)" d="M 393.6 460.1 L 393.5 453.6 L 346.7 426.6 C 343.3 424.70000000000005 340.59999999999997 419.40000000000003 340.59999999999997 415 L 340.59999999999997 388.8 L 335.29999999999995 385.2 L 343.69999999999993 380.4 L 343.69999999999993 380.5 C 344.79999999999995 380.3 345.99999999999994 380.5 347.19999999999993 381.2 L 397.19999999999993 410.09999999999997 C 400.5999999999999 411.99999999999994 403.29999999999995 417.2 403.29999999999995 421.7 L 403.29999999999995 450.59999999999997 C 403.29999999999995 452.7 402.69999999999993 454.4 401.59999999999997 455.4 L 401.7 455.4 L 401 455.9 z M 337.20000000000005 385.3 L 341.6 388.3 L 341.6 415 C 341.6 419.1 344.1 424 347.20000000000005 425.8 L 394.50000000000006 453.1 L 394.6000000000001 458.40000000000003 L 399.80000000000007 455.40000000000003 L 400.50000000000006 455.00000000000006 C 401.6000000000001 454.30000000000007 402.30000000000007 452.70000000000005 402.30000000000007 450.6000000000001 L 402.30000000000007 421.7000000000001 C 402.30000000000007 417.6000000000001 399.80000000000007 412.7000000000001 396.70000000000005 410.9000000000001 L 346.70000000000005 382.1000000000001 C 345.50000000000006 381.4000000000001 344.40000000000003 381.2000000000001 343.50000000000006 381.7000000000001 L 343.30000000000007 381.8000000000001 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -156.23 2.02)" id="<Compound Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-369.55, -420.06)" d="M 403.3 421.7 L 403.3 450.59999999999997 C 403.3 452.99999999999994 402.5 454.79999999999995 401.2 455.7 Q 401.09999999999997 455.8 401 455.8 L 394.4 459.6 Q 394.2 459.70000000000005 394.09999999999997 459.70000000000005 Q 393.99999999999994 459.70000000000005 393.9 459.6 C 393.7 459.5 393.59999999999997 459.40000000000003 393.59999999999997 459.20000000000005 L 393.49999999999994 453.6 L 346.69999999999993 426.6 C 343.29999999999995 424.70000000000005 340.5999999999999 419.40000000000003 340.5999999999999 415 L 340.5999999999999 388.8 L 335.9999999999999 385.7 Q 335.7999999999999 385.5 335.7999999999999 385.2 Q 335.7999999999999 385 335.9999999999999 384.8 L 342.9999999999999 380.8 Q 343.0999999999999 380.8 343.1999999999999 380.7 C 344.2999999999999 380.2 345.6999999999999 380.4 347.1999999999999 381.2 L 397.1999999999999 410.09999999999997 C 400.59999999999985 411.99999999999994 403.2999999999999 417.2 403.2999999999999 421.7 z M 402.3 421.7 C 402.3 417.5 399.8 412.7 396.7 410.9 L 346.7 382 C 345.5 381.3 344.4 381.2 343.5 381.7 Q 343.4 381.7 343.4 381.7 L 337.2 385.3 L 341.3 388.1 Q 341.5 388.3 341.5 388.5 L 341.5 415 C 341.5 419.1 344.1 424 347.2 425.8 L 394.2 452.90000000000003 C 394.4 453.00000000000006 394.5 453.20000000000005 394.5 453.3 L 394.6 458.40000000000003 L 400.3 455.1 Q 400.40000000000003 455 400.5 455 C 401.7 454.3 402.3 452.7 402.3 450.6 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -160.28 4.05)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(240,201,59); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-365.5, -422.1)" d="M 390.5 414.2 L 340.5 385.4 C 337.2 383.5 334.6 385.5 334.6 389.79999999999995 L 334.6 418.69999999999993 C 334.6 422.99999999999994 337.20000000000005 428.0999999999999 340.5 429.8999999999999 L 374.5 449.5999999999999 L 378.1 451.5999999999999 L 390.5 458.7999999999999 C 393.7 460.6999999999999 396.4 458.6999999999999 396.4 454.3999999999999 L 396.4 425.49999999999994 C 396.4 421.09999999999997 393.7 416.09999999999997 390.5 414.19999999999993 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -173.83 -3.65)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-351.95, -414.4)" d="M 351.9 409.6 C 349.59999999999997 408.3 347.79999999999995 409.40000000000003 347.79999999999995 412 C 347.79999999999995 414.6 349.59999999999997 417.9 351.9 419.2 C 354.2 420.5 356.09999999999997 419.4 356.09999999999997 416.8 C 356.09999999999997 414.1 354.2 410.90000000000003 351.9 409.6 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -173.83 -3.65)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-351.95, -414.39)" d="M 355.6 416.8 C 355.6 417.90000000000003 355.3 418.7 354.6 419.1 C 354 419.40000000000003 353.1 419.3 352.20000000000005 418.8 C 350.00000000000006 417.5 348.30000000000007 414.5 348.30000000000007 412 C 348.30000000000007 410.9 348.6000000000001 410.1 349.20000000000005 409.7 C 349.90000000000003 409.4 350.70000000000005 409.5 351.70000000000005 410 L 352.1 410.3 L 352.6 409.40000000000003 L 352.20000000000005 409.20000000000005 C 350.90000000000003 408.50000000000006 349.70000000000005 408.40000000000003 348.80000000000007 408.90000000000003 C 347.80000000000007 409.40000000000003 347.30000000000007 410.50000000000006 347.30000000000007 412.00000000000006 C 347.30000000000007 414.80000000000007 349.30000000000007 418.20000000000005 351.70000000000005 419.6000000000001 Q 352.80000000000007 420.2000000000001 353.80000000000007 420.2000000000001 Q 354.50000000000006 420.2000000000001 355.1000000000001 419.9000000000001 C 356.00000000000006 419.30000000000007 356.6000000000001 418.2000000000001 356.6000000000001 416.80000000000007 L 356.6000000000001 416.30000000000007 L 355.6000000000001 416.30000000000007 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -161.48 3.85)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-364.3, -421.9)" d="M 364.3 417.1 C 362 415.8 360.2 416.90000000000003 360.2 419.5 C 360.2 422.1 362 425.4 364.3 426.7 C 366.6 428 368.40000000000003 426.9 368.40000000000003 424.3 C 368.40000000000003 421.6 366.6 418.40000000000003 364.3 417.1 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -161.48 3.85)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-364.3, -421.89)" d="M 368 424.3 C 368 425.40000000000003 367.6 426.2 367 426.6 C 366.4 426.90000000000003 365.5 426.8 364.5 426.3 C 362.4 425 360.6 422 360.6 419.5 C 360.6 418.4 361 417.6 361.6 417.2 C 362.20000000000005 416.9 363.1 417 364.1 417.5 L 364.5 417.8 L 365 416.90000000000003 L 364.5 416.70000000000005 C 363.3 416.00000000000006 362.1 415.90000000000003 361.1 416.40000000000003 C 360.20000000000005 416.90000000000003 359.70000000000005 418.00000000000006 359.70000000000005 419.50000000000006 C 359.70000000000005 422.30000000000007 361.6 425.70000000000005 364.1 427.1000000000001 Q 365.20000000000005 427.7000000000001 366.20000000000005 427.7000000000001 Q 366.90000000000003 427.7000000000001 367.50000000000006 427.4000000000001 C 368.40000000000003 426.80000000000007 368.90000000000003 425.7000000000001 368.90000000000003 424.30000000000007 L 368.90000000000003 423.80000000000007 L 368.00000000000006 423.80000000000007 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -149.13 11.35)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-376.65, -429.4)" d="M 376.7 424.6 C 374.4 423.3 372.5 424.40000000000003 372.5 427 C 372.5 429.6 374.4 432.9 376.7 434.2 C 379 435.5 380.8 434.4 380.8 431.8 C 380.8 429.1 379 425.90000000000003 376.7 424.6 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -149.13 11.35)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-376.65, -429.39)" d="M 380.3 431.8 C 380.3 432.90000000000003 380 433.7 379.40000000000003 434.1 C 378.70000000000005 434.40000000000003 377.90000000000003 434.3 376.90000000000003 433.8 C 374.70000000000005 432.5 373.00000000000006 429.5 373.00000000000006 427 C 373.00000000000006 425.9 373.30000000000007 425.1 374.00000000000006 424.7 C 374.6000000000001 424.4 375.50000000000006 424.5 376.40000000000003 425 L 376.8 425.3 L 377.3 424.40000000000003 L 376.90000000000003 424.20000000000005 C 375.6 423.50000000000006 374.40000000000003 423.40000000000003 373.50000000000006 423.90000000000003 C 372.6000000000001 424.40000000000003 372.00000000000006 425.50000000000006 372.00000000000006 427.00000000000006 C 372.00000000000006 429.80000000000007 374.00000000000006 433.20000000000005 376.40000000000003 434.6000000000001 Q 377.50000000000006 435.2000000000001 378.50000000000006 435.2000000000001 Q 379.20000000000005 435.2000000000001 379.80000000000007 434.9000000000001 C 380.80000000000007 434.30000000000007 381.30000000000007 433.2000000000001 381.30000000000007 431.80000000000007 L 381.30000000000007 431.30000000000007 L 380.30000000000007 431.30000000000007 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -160.33 4.03)" id="<Path>"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(30,30,30); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-365.45, -422.07)" d="M 390.7 413.8 L 340.7 385 C 339.2 384 337.59999999999997 383.9 336.4 384.6 C 335 385.5 334.09999999999997 387.40000000000003 334.09999999999997 389.8 L 334.09999999999997 418.7 C 334.09999999999997 423.2 336.9 428.4 340.2 430.3 L 374.9 450.3 L 375.4 449.5 L 340.7 429.5 C 337.59999999999997 427.7 335.09999999999997 422.9 335.09999999999997 418.7 L 335.09999999999997 389.8 C 335.09999999999997 387.7 335.7 386.1 336.9 385.5 C 337.79999999999995 384.9 339 385.1 340.2 385.8 L 390.2 414.7 C 393.3 416.4 395.9 421.3 395.9 425.5 L 395.9 454.4 C 395.9 456.5 395.2 458 394.09999999999997 458.7 C 393.2 459.2 391.99999999999994 459.09999999999997 390.7 458.4 L 377 450.5 L 376.6 451.3 L 390.20000000000005 459.2 Q 391.6 460 392.80000000000007 460 Q 393.80000000000007 460 394.6000000000001 459.5 C 396.00000000000006 458.7 396.80000000000007 456.8 396.80000000000007 454.4 L 396.80000000000007 425.5 C 396.80000000000007 421 394.1000000000001 415.8 390.70000000000005 413.8 z" stroke-linecap="round" />
</g>
</g>
</g>
</svg>