{"codeToName": {"32": "space", "160": "space", "33": "exclam", "34": "quotedbl", "35": "numbersign", "36": "dollar", "37": "percent", "38": "ampersand", "146": "quoteright", "40": "parenleft", "41": "parenright", "42": "asterisk", "43": "plus", "44": "comma", "45": "hyphen", "173": "hyphen", "46": "period", "47": "slash", "48": "zero", "49": "one", "50": "two", "51": "three", "52": "four", "53": "five", "54": "six", "55": "seven", "56": "eight", "57": "nine", "58": "colon", "59": "semicolon", "60": "less", "61": "equal", "62": "greater", "63": "question", "64": "at", "65": "A", "66": "B", "67": "C", "68": "D", "69": "E", "70": "F", "71": "G", "72": "H", "73": "I", "74": "J", "75": "K", "76": "L", "77": "M", "78": "N", "79": "O", "80": "P", "81": "Q", "82": "R", "83": "S", "84": "T", "85": "U", "86": "V", "87": "W", "88": "X", "89": "Y", "90": "Z", "91": "bracketleft", "92": "backslash", "93": "bracketright", "94": "asciicircum", "95": "underscore", "145": "quoteleft", "97": "a", "98": "b", "99": "c", "100": "d", "101": "e", "102": "f", "103": "g", "104": "h", "105": "i", "106": "j", "107": "k", "108": "l", "109": "m", "110": "n", "111": "o", "112": "p", "113": "q", "114": "r", "115": "s", "116": "t", "117": "u", "118": "v", "119": "w", "120": "x", "121": "y", "122": "z", "123": "braceleft", "124": "bar", "125": "braceright", "126": "asciitilde", "161": "exclamdown", "162": "cent", "163": "sterling", "165": "yen", "131": "florin", "167": "section", "164": "currency", "39": "<PERSON><PERSON><PERSON>", "147": "quotedblleft", "171": "guillemotleft", "139": "guil<PERSON>lle<PERSON>", "155": "guil<PERSON><PERSON><PERSON>", "150": "endash", "134": "dagger", "135": "daggerdbl", "183": "periodcentered", "182": "paragraph", "149": "bullet", "130": "quotesinglbase", "132": "quotedblbase", "148": "<PERSON><PERSON><PERSON><PERSON>", "187": "guil<PERSON><PERSON><PERSON>", "133": "ellipsis", "137": "perth<PERSON>and", "191": "questiondown", "96": "grave", "180": "acute", "136": "circumflex", "152": "tilde", "175": "macron", "168": "<PERSON><PERSON><PERSON>", "184": "cedilla", "151": "emdash", "198": "AE", "170": "ordfeminine", "216": "<PERSON><PERSON><PERSON>", "140": "OE", "186": "ordmasculine", "230": "ae", "248": "oslash", "156": "oe", "223": "germandbls", "207": "Idieresis", "233": "eacute", "159": "Ydieresis", "247": "divide", "221": "Ya<PERSON>", "194": "Acircumflex", "225": "aacute", "219": "Ucircumflex", "253": "yacute", "234": "ecircumflex", "220": "Udieresis", "218": "Uacute", "203": "Edieresis", "169": "copyright", "229": "aring", "224": "agrave", "227": "atilde", "154": "scaron", "237": "iacute", "251": "ucircumflex", "226": "acircumflex", "231": "ccedilla", "222": "Thorn", "179": "threesuperior", "210": "<PERSON><PERSON>", "192": "<PERSON><PERSON>", "215": "multiply", "250": "uacute", "255": "ydieresis", "238": "icircumflex", "202": "Ecircumflex", "228": "adieresis", "235": "edieresis", "205": "Iacute", "177": "plus<PERSON>us", "166": "brokenbar", "174": "registered", "200": "<PERSON><PERSON>", "142": "<PERSON><PERSON><PERSON>", "208": "Eth", "199": "Ccedilla", "193": "Aacute", "196": "Adieresis", "232": "egrave", "211": "Oacute", "243": "oacute", "239": "idieresis", "212": "Ocircumflex", "217": "<PERSON><PERSON>", "254": "thorn", "178": "twosuperior", "214": "Odieresis", "181": "mu", "236": "igrave", "190": "threequarters", "153": "trademark", "204": "<PERSON><PERSON>", "189": "onehalf", "244": "ocircumflex", "241": "ntilde", "201": "Eacute", "188": "onequarter", "138": "<PERSON><PERSON><PERSON>", "176": "degree", "242": "ograve", "249": "ugrave", "209": "Ntilde", "245": "otilde", "195": "<PERSON><PERSON>", "197": "<PERSON><PERSON>", "213": "<PERSON><PERSON><PERSON>", "206": "Icircumflex", "172": "logicalnot", "246": "odieresis", "252": "udieresis", "240": "eth", "158": "z<PERSON>on", "185": "onesuperior", "128": "Euro"}, "isUnicode": false, "FontName": "Times-Italic", "FullName": "Times Italic", "FamilyName": "Times", "Weight": "Medium", "ItalicAngle": "-15.5", "IsFixedPitch": "false", "CharacterSet": "ExtendedRoman", "FontBBox": ["-169", "-217", "1010", "883"], "UnderlinePosition": "-100", "UnderlineThickness": "50", "Version": "002.000", "EncodingScheme": "WinAnsiEncoding", "CapHeight": "653", "XHeight": "441", "Ascender": "683", "Descender": "-217", "StdHW": "32", "StdVW": "76", "StartCharMetrics": "317", "C": {"32": 250, "160": 250, "33": 333, "34": 420, "35": 500, "36": 500, "37": 833, "38": 778, "146": 333, "40": 333, "41": 333, "42": 500, "43": 675, "44": 250, "45": 333, "173": 333, "46": 250, "47": 278, "48": 500, "49": 500, "50": 500, "51": 500, "52": 500, "53": 500, "54": 500, "55": 500, "56": 500, "57": 500, "58": 333, "59": 333, "60": 675, "61": 675, "62": 675, "63": 500, "64": 920, "65": 611, "66": 611, "67": 667, "68": 722, "69": 611, "70": 611, "71": 722, "72": 722, "73": 333, "74": 444, "75": 667, "76": 556, "77": 833, "78": 667, "79": 722, "80": 611, "81": 722, "82": 611, "83": 500, "84": 556, "85": 722, "86": 611, "87": 833, "88": 611, "89": 556, "90": 556, "91": 389, "92": 278, "93": 389, "94": 422, "95": 500, "145": 333, "97": 500, "98": 500, "99": 444, "100": 500, "101": 444, "102": 278, "103": 500, "104": 500, "105": 278, "106": 278, "107": 444, "108": 278, "109": 722, "110": 500, "111": 500, "112": 500, "113": 500, "114": 389, "115": 389, "116": 278, "117": 500, "118": 444, "119": 667, "120": 444, "121": 444, "122": 389, "123": 400, "124": 275, "125": 400, "126": 541, "161": 389, "162": 500, "163": 500, "fraction": 167, "165": 500, "131": 500, "167": 500, "164": 500, "39": 214, "147": 556, "171": 500, "139": 333, "155": 333, "fi": 500, "fl": 500, "150": 500, "134": 500, "135": 500, "183": 250, "182": 523, "149": 350, "130": 333, "132": 556, "148": 556, "187": 500, "133": 889, "137": 1000, "191": 500, "96": 333, "180": 333, "136": 333, "152": 333, "175": 333, "breve": 333, "dotaccent": 333, "168": 333, "ring": 333, "184": 333, "hungarumlaut": 333, "ogonek": 333, "caron": 333, "151": 889, "198": 889, "170": 276, "Lslash": 556, "216": 722, "140": 944, "186": 310, "230": 667, "dotlessi": 278, "lslash": 278, "248": 500, "156": 667, "223": 500, "207": 333, "233": 444, "abreve": 500, "uhungarumlaut": 500, "ecaron": 444, "159": 556, "247": 675, "221": 556, "194": 611, "225": 500, "219": 722, "253": 444, "scommaaccent": 389, "234": 444, "Uring": 722, "220": 722, "aogonek": 500, "218": 722, "uogonek": 500, "203": 611, "Dcroat": 722, "commaaccent": 250, "169": 760, "Emacron": 611, "ccaron": 444, "229": 500, "Ncommaaccent": 667, "lacute": 278, "224": 500, "Tcommaaccent": 556, "Cacute": 667, "227": 500, "Edotaccent": 611, "154": 389, "scedilla": 389, "237": 278, "lozenge": 471, "Rcaron": 611, "Gcommaaccent": 722, "251": 500, "226": 500, "Amacron": 611, "rcaron": 389, "231": 444, "Zdotaccent": 556, "222": 611, "Omacron": 722, "Racute": 611, "Sacute": 500, "dcaron": 544, "Umacron": 722, "uring": 500, "179": 300, "210": 722, "192": 611, "Abreve": 611, "215": 675, "250": 500, "Tcaron": 556, "partialdiff": 476, "255": 444, "Nacute": 667, "238": 278, "202": 611, "228": 500, "235": 444, "cacute": 444, "nacute": 500, "umacron": 500, "Ncaron": 667, "205": 333, "177": 675, "166": 275, "174": 760, "Gbreve": 722, "Idotaccent": 333, "summation": 600, "200": 611, "racute": 389, "omacron": 500, "Zacute": 556, "142": 556, "greaterequal": 549, "208": 722, "199": 667, "lcommaaccent": 278, "tcaron": 300, "eogonek": 444, "Uogonek": 722, "193": 611, "196": 611, "232": 444, "zacute": 389, "iogonek": 278, "211": 722, "243": 500, "amacron": 500, "sacute": 389, "239": 278, "212": 722, "217": 722, "Delta": 612, "254": 500, "178": 300, "214": 722, "181": 500, "236": 278, "ohungarumlaut": 500, "Eogonek": 611, "dcroat": 500, "190": 750, "Scedilla": 500, "lcaron": 300, "Kcommaaccent": 667, "Lacute": 556, "153": 980, "edotaccent": 444, "204": 333, "Imacron": 333, "Lcaron": 611, "189": 750, "lessequal": 549, "244": 500, "241": 500, "Uhungarumlaut": 722, "201": 611, "emacron": 444, "gbreve": 500, "188": 750, "138": 500, "Scommaaccent": 500, "Ohungarumlaut": 722, "176": 400, "242": 500, "Ccaron": 667, "249": 500, "radical": 453, "Dcaron": 722, "rcommaaccent": 389, "209": 667, "245": 500, "Rcommaaccent": 611, "Lcommaaccent": 556, "195": 611, "Aogonek": 611, "197": 611, "213": 722, "zdotaccent": 389, "Ecaron": 611, "Iogonek": 333, "kcommaaccent": 444, "minus": 675, "206": 333, "ncaron": 500, "tcommaaccent": 278, "172": 675, "246": 500, "252": 500, "notequal": 549, "gcommaaccent": 500, "240": 500, "158": 389, "ncommaaccent": 500, "185": 300, "imacron": 278, "128": 500}, "CIDtoGID_Compressed": true, "CIDtoGID": "eJwDAAAAAAE=", "_version_": 6}