<?php
require("php/common.php");
//echo checkSession();
if(checkSession()=="true"){
  echo "<script>window.location.href='question?qn=1';</script>";
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register | Mindscan</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap"
        rel="stylesheet">
    <link href="css/bootstrap.min.css?ver=1.1.0" rel="stylesheet" />
    <link href="css/mdb.css?ver=1.1.0" rel="stylesheet" />
    <link href='https://fonts.googleapis.com/css?family=Hind' rel='stylesheet'>
    <link href="css/main.css?ver=1.1.0" rel="stylesheet" />
    <link href="css/styles.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <script src="https://bookings.mendingmind.org/scripts/flatpickr.js"></script>
    <link rel="icon" href="./img/favicon.png" type="image/png">
</head>

<body>
    <section class="login-page px-2">
        <div class="container">
            <div class="row">
                <div class="mb-2 align-items-end col-12 d-flex justify-content-between mt-3">
                    <h1 class="text-uppercase mb-0 font-weight-bold text-theme-dark">
                        Hello,
                    </h1>
                    <img src="./img/bulb.png" class="bulb" alt="Bulb">
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <form action="php/login.php" id="login" method="post">
                    <div class="container">
                        <div class="row">
                            <div class="col-12 form-group mb-0">
                                <div class="form-group mb-2">
                                    <label for="name">Name</label>
                                    <input required type="text" class="form-control" id="name" aria-describedby="name"
                                        name="name" data-type="name" data-validate="onkeyup">
                                    <small id="name_error" class="form-text text-danger"></small>
                                </div>
                            </div>
                            <div class="col-12 form-group mb-0">
                                <div class="form-group mb-2">
                                    <label for="age">Age</label>
                                    <input required type="number" min="0" max="100" class="form-control" id="age"
                                        aria-describedby="age" data-type="age" data-validate="onkeyup" name="age">
                                    <small id="age_error" class="form-text text-danger"></small>
                                </div>
                            </div>
                            <div class="col-12 form-group mb-0">
                                <div class="form-group mb-2">
                                    <label for="emailid">Email Id</label>
                                    <input required type="email" class="form-control" id="emailid"
                                        aria-describedby="emailid" name="emailid" data-type="email"
                                        data-validate="onchange">
                                    <small id="emailid_error" class="form-text text-danger"></small>
                                </div>
                            </div>
                            <div class="col-12 form-group mb-0">
                                <div class="form-group mb-2">
                                    <label for="phone">Contact No</label>
                                    <input required type="number" minlength="10" maxlength="10" class="form-control"
                                        id="phone" aria-describedby="phone" name="phone" data-type="number"
                                        data-onblur_type="phone" data-validate="onkeyup">
                                    <small id="phone_error" class="form-text text-danger"></small>
                                </div>
                            </div>
                            <div class="col-12 form-group mb-0">
                                <div class="form-group mb-2">
                                    <label for="location">Location</label>
                                    <input required type="text" class="form-control" id="location"
                                        aria-describedby="location" name="location" data-type="text-numbers"
                                        data-validate="onkeyup">
                                    <small id="location_error" class="form-text text-danger"></small>
                                </div>
                            </div>
                            <div class="col-12 form-group mb-0">
                                <div class="form-group mb-2">
                                    <label for="gender">Gender</label>
                                    <select required class="form-control" id="gender" aria-describedby="gender"
                                        name="gender">
                                        <option value="#">Select Gender</option>
                                        <option value="male">Male</option>
                                        <option value="female">Female</option>
                                        <option value="others">Others</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-12 form-group mb-0">
                                <div class="form-group mb-2">
                                    <label for="marital-status">Marial Status</label>
                                    <select required class="form-control" id="marital-status"
                                        aria-describedby="marital-status" name="marital-status"">
                                        <option value=" #">Select Marial Status</option>
                                        <option value="married">Married</option>
                                        <option value="single">Single</option>
                                        <option value="divorced">Divorced</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-12 form-group mb-0">
                                <div class="form-group mb-2">
                                    <label for="profession">Profession</label>
                                    <input required type="text" class="form-control" id="profession"
                                        aria-describedby="profession" name="profession" data-type="name"
                                        data-validate="onkeyup">
                                    <small id="profession_error" class="form-text text-danger"></small>
                                </div>
                            </div>
                            <div class="col-12 form-group mb-0">
                                <div class="form-group mb-2">
                                    <label for="dateOfAssessment">Date of Assessment</label>
                                    <input required type="date" class="form-control" id="dateOfAssessment"
                                        aria-describedby="dateOfAssessment" name="dateOfAssessment">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12 px-0 submit-wrapper position-relative">
                            <img src="./img/login-bg.png" alt="Login BG" class="img-fluid">
                            <button type="submit" class="btn d-flex align-items-center mt-5 mx-auto shadow-none">
                                <span class="left-arrow">></span>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </section>
    <div class="modal fade" id="languageModal" data-backdrop="static" data-keyboard="false" tabindex="-1"
        aria-labelledby="languageModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-sm">
            <div class="modal-content mx-3 bg-theme-yellow rounded-theme">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-12">
                            <h4 class="text-theme-dark text-center font-weight-bold">
                                LANGUAGE/भाषा
                            </h4>
                        </div>
                        <div class="col-10 d-flex flex-column justify-content-center mx-auto">
                            <button data-language="en"
                                class="lang-selector text-capitalize font-weight-500 btn bg-white rounded-theme">
                                English/अंग्रेज़ी
                            </button>
                            <button data-language="hi"
                                class="lang-selector text-capitalize font-weight-500 btn bg-white rounded-theme">
                                Hindi/हिन्दी
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script type="text/javascript" src="scripts/jquery.min.js?ver=1.1.0"></script>
    <script type="text/javascript" src="scripts/popper.min.js?ver=1.1.0"></script>
    <script type="text/javascript" src="scripts/bootstrap.min.js?ver=1.1.0"></script>
    <script type="text/javascript" src="scripts/wow.min.js?ver=1.1.0"></script>
    <script type="text/javascript" src="scripts/mdb.min.js?ver=1.1.0"></script>
    <script src="scripts/main.js"></script>
    <script>
        new WOW().init();
        $(document).ready(function () {
            $('#languageModal').modal('show')
            var x = flatpickr("#dateOfAssessment", {
                altInput: true,
                altFormat: "F j, Y",
                dateFormat: "Y-m-d",
                minDate: "today",
                defaultDate: "today",
                disableMobile:"false"
            });
        })

    </script>

</body>

</html>