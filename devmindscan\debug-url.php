<?php
// Debug URL generation
require("php/common.php");

echo "<h1>URL Debug Information</h1>";

echo "<h2>Server Variables:</h2>";
echo "HTTP_HOST: " . $_SERVER['HTTP_HOST'] . "<br>";
echo "REQUEST_URI: " . $_SERVER['REQUEST_URI'] . "<br>";
echo "SERVER_PORT: " . (isset($_SERVER['SERVER_PORT']) ? $_SERVER['SERVER_PORT'] : 'Not set') . "<br>";
echo "HTTPS: " . (isset($_SERVER['HTTPS']) ? $_SERVER['HTTPS'] : 'Not set') . "<br>";
echo "HTTP_X_FORWARDED_PROTO: " . (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) ? $_SERVER['HTTP_X_FORWARDED_PROTO'] : 'Not set') . "<br>";
echo "HTTP_X_FORWARDED_SSL: " . (isset($_SERVER['HTTP_X_FORWARDED_SSL']) ? $_SERVER['HTTP_X_FORWARDED_SSL'] : 'Not set') . "<br>";

echo "<h2>Generated URLs:</h2>";
echo "WebURL: " . $webUrl . "<br>";
echo "Reports URL: " . $webUrl . "reports.php<br>";
echo "Question URL: " . $webUrl . "question?qn=1<br>";

echo "<h2>Test Links:</h2>";
echo '<a href="' . $webUrl . 'reports.php">Test Reports Link</a><br>';
echo '<a href="' . $webUrl . 'question?qn=1">Test Question Link</a><br>';
echo '<a href="reports.php">Relative Reports Link</a><br>';

echo "<h2>Manual URL Construction Test:</h2>";
$protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https://' : 'http://';
$host = $_SERVER['HTTP_HOST'];
$cleanHost = str_replace(':8880', '', $host);

echo "Original host: " . $host . "<br>";
echo "Clean host: " . $cleanHost . "<br>";
echo "Protocol: " . $protocol . "<br>";
echo "Manual URL: " . $protocol . $cleanHost . "/<br>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2 { color: #333; }
a { display: block; margin: 5px 0; padding: 5px; background: #f0f0f0; text-decoration: none; }
</style>
