$(document).ready(function () {
    // const min = new Date().toISOString().split("T")[0];
    // $("#dateOfAssessment").val(min);
    // $("#dateOfAssessment").attr("min", min);
    $(".lang-selector").click(function(e){
        e.preventDefault();
        const lang=$(this).data('language');
        console.log("Sele",lang);
        document.cookie = "lang="+lang+"";
        $("#languageModal").modal('hide');
        window.location.href='./login'
    });

    $(".btn-close").click(function () {
        $("#exampleModal").modal('hide');
    })

    $("#login").submit(function (e) {
        e.preventDefault();
        let checks = [];
        document.querySelectorAll("#login")[0].forEach(field => {
            let [status, msg] = validate(field.value, field.name, true);
            checks.push(status);
            if (!status) {
                document.getElementById(field.name + "_error").innerText = msg;
            }
            else {
                $("#" + field.name + "_error").text("");
            }
        });
        if (!checks.includes(false)) {
            $("#login").unbind('submit').submit();
        }
    });

    $(`input[data-validate="onkeyup"]`).each(function (e) {
        var input = $(this);
        $(input).keyup(function (e) {
            if (input.data("type")) {
                var type = input.data("type");
                let [status, msg] = validate(input.val(), type, true);
                if (!status) {
                    document.getElementById(input.attr('name') + "_error").innerText = msg;
                    document.getElementById(input.attr('id')).value = input.val().substring(0, input.val().length - 1);
                }
                else {
                    document.getElementById(input.attr('name') + "_error").innerText = msg;
                }
            }
        });
    })

    $(`input[data-validate="onchange"]`).each(function (e) {
        var input = $(this);
        $(input).change(function (e) {
            if (input.data("type")) {
                var type = input.data("type");
                let [status, msg] = validate(input.val(), type, true);
                if (!status) {
                    document.getElementById(input.attr('name') + "_error").innerText = msg;
                    document.getElementById(input.attr('id')).value = input.val().substring(0, input.val().length - 1);
                }
                else {
                    document.getElementById(input.attr('name') + "_error").innerText = msg;
                }
            }
        })
    });

    $("form#login input").blur(function (e) {
        var input = $(this);
        if (input.data('type')) {
            var type = input.data('onblur_type') ? input.data('onblur_type') : input.data('type');
            let [status, msg] = validate(input.val(), type, true);
            if (!status) {
                document.getElementById(input.attr('name') + "_error").innerText = msg;
                // document.getElementById(e.currentTarget.id).value = input.val().substring(0, input.val().length - 1);
            }
            else {
                document.getElementById(input.attr('name') + "_error").innerText = msg;
            }
        }
    });

    // $("#questions").submit(function(e){
    //     e.preventDefault();
    //     if(e.currentTarget.id.value=="Q26"){
    //         $('#exampleModal').modal('show');
    //         setTimeout(function(){
    //             $("#exampleModal").hide();
    //             $("#questions").unbind('submit').submit();
    //         }, 4000);
    //     }
    //     else{
    //         $("#questions").unbind('submit').submit();
    //     }
    // });

    $("#option1,#option1 *").click(function (e) {
        e.preventDefault();
        $("#option1 input").prop('checked', true);
        $("#questions").submit();
    })

    $("#option1_mob,#option1_mob *").click(function (e) {
        e.preventDefault();
        let id = e.target.getAttribute("data-id") != null && e.target.getAttribute("data-id") != undefined && e.target.getAttribute("data-id").length > 0 ? e.target.getAttribute("data-id") : e.target.getAttribute("for");
        $("#option1_mob input").prop('checked', true);
        $("#questions").submit();
    })

    $("#option3,#option3 *").click(function (e) {
        e.preventDefault();
        $("#option3 input").prop('checked', true);
        $("#questions").submit();
    })

    $("#option3_mob,#option3_mob *").click(function (e) {
        e.preventDefault();
        $("#option3_mob input").prop('checked', true);
        $("#questions").submit();
    })
    $("#option4,#option4 *").click(function (e) {
        e.preventDefault();
        $("#option4 input").prop('checked', true);
        $("#questions").submit();
    })

    $("#option4_mob,#option4_mob *").click(function (e) {
        e.preventDefault();
        $("#option4_mob input").prop('checked', true);
        $("#questions").submit();
    })
    $("#option2,#option2 *").click(function (e) {
        e.preventDefault();
        $("#option2 input").prop('checked', true);
        $("#questions").submit();
    })

    $("#option2_mob,#option2_mob *").click(function (e) {
        e.preventDefault();
        $("#option2_mob input").prop('checked', true);
        $("#questions").submit();
    })

})

function checkData(value, name) {
    let [status, msg] = validate(value, name, true);
    if (!status) {
        document.getElementById(name + "_error").innerText = msg;
        //document.getElementById(e.currentTarget.id).value = e.target.value.substring(0, e.target.value.length - 1);        
    }
    else {
        try {
            document.getElementById(name + "_error").innerText = " ";
        } catch (error) {
            console.log("error")
        }
    }
    return status
}

function validate(string, type, allowedEmpty = false) {
    let min_age = 0;
    let max_age = 100;
    if (string.length == 0 && !allowedEmpty)
        return false, "Field cannot be empty!";
    switch (type) {
        case "name":
            if (checkRegex(/^[a-zA-Z ]*$/, string))
                return [true, ""];
            else
                return [false, "Please enter a valid name!"];
        case "phone":
            if (checkRegex(/^(0|91)?[6-9][0-9]{9}$/, string))
                return [true, ""];
            else
                return [false, "Please enter a valid phone number!"];
        case "email":
            if (checkRegex(/^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|.(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/, string))
                return [true, ""];
            else
                return [false, "Please enter a valid email!"];
        case "text-numbers":
            if (checkRegex(/^[a-zA-Z0-9- ]*$/, string))
                return [true, ""];
            else
                return [false, "Please enter a valid email!"];
        case "age":
            if (Number(string) > min_age && Number(string) < max_age)
                return [true, ""];
            else
                return [false, "Please enter a valid age!"];
        default:
            return [true, ""];
    }
}

function checkRegex(regex, string) {
    if (string.match(regex)) {
        return true;
    }
    return false;
}
