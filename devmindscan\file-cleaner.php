<?php
// File cleaner to remove BOM and fix encoding issues
$filesToCheck = [
    'minimal-session-test.php',
    'php/common.php',
    'test-session.php'
];

echo "<h1>File Cleaner</h1>";

foreach ($filesToCheck as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        $originalSize = strlen($content);
        
        // Remove BOM if present
        if (substr($content, 0, 3) === "\xEF\xBB\xBF") {
            $content = substr($content, 3);
            echo "$file: Removed UTF-8 BOM<br>";
        }
        
        // Remove any whitespace before <?php
        $content = ltrim($content);
        
        // Ensure it starts with <?php
        if (!preg_match('/^<\?php/', $content)) {
            echo "$file: ❌ Doesn't start with &lt;?php<br>";
        } else {
            echo "$file: ✅ Starts correctly<br>";
        }
        
        $newSize = strlen($content);
        if ($originalSize !== $newSize) {
            // Backup original
            file_put_contents($file . '.backup', file_get_contents($file));
            // Write cleaned version
            file_put_contents($file, $content);
            echo "$file: Cleaned (size: $originalSize → $newSize)<br>";
        } else {
            echo "$file: No changes needed<br>";
        }
    } else {
        echo "$file: File not found<br>";
    }
}

echo "<br><a href='minimal-session-test.php'>Test Minimal Session</a><br>";
echo "<a href='clean-session-test.php'>Test Clean Session</a><br>";
echo "<a href='diagnostic.php'>Run Diagnostics</a><br>";
?>
