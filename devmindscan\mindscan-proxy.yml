version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.simple
    container_name: mindscan-app-p4k844kg44g0gg4gs8gko0wo-073655242830
    restart: always
    volumes:
      - ./generated_reports:/var/www/html/generated_reports
      - ./reports:/var/www/html/reports
    labels:
      - coolify.managed=true
      - coolify.version=4.0.0-beta.420.3
      - coolify.applicationId=6
      - coolify.type=application
      - coolify.name=mindscan-app-p4k844kg44g0gg4gs8gko0wo-073655242830
      - coolify.resourceName=mindscan-mendingmind-org
      - coolify.projectName=mindscan-project
      - coolify.serviceName=mindscan-mendingmind-org
      - coolify.environmentName=production
      - coolify.pullRequestId=0
      - traefik.enable=true
      - traefik.http.middlewares.gzip.compress=true
      - traefik.http.middlewares.redirect-to-https.redirectscheme.scheme=https
      - traefik.http.routers.http-0-p4k844kg44g0gg4gs8gko0wo-app.entryPoints=http
      - traefik.http.routers.http-0-p4k844kg44g0gg4gs8gko0wo-app.middlewares=redirect-to-https
      - 'traefik.http.routers.http-0-p4k844kg44g0gg4gs8gko0wo-app.rule=Host(`mindscan.mendingmind.org`) && PathPrefix(`/`)'
      - traefik.http.routers.https-0-p4k844kg44g0gg4gs8gko0wo-app.entryPoints=https
      - traefik.http.routers.https-0-p4k844kg44g0gg4gs8gko0wo-app.middlewares=gzip
      - 'traefik.http.routers.https-0-p4k844kg44g0gg4gs8gko0wo-app.rule=Host(`mindscan.mendingmind.org`) && PathPrefix(`/`)'
      - traefik.http.routers.https-0-p4k844kg44g0gg4gs8gko0wo-app.tls.certresolver=letsencrypt
      - traefik.http.routers.https-0-p4k844kg44g0gg4gs8gko0wo-app.tls=true
      - traefik.http.services.p4k844kg44g0gg4gs8gko0wo-app.loadbalancer.server.port=80
    networks:
      p4k844kg44g0gg4gs8gko0wo: null

volumes: {}

networks:
  p4k844kg44g0gg4gs8gko0wo:
    name: p4k844kg44g0gg4gs8gko0wo
    external: true

configs: {}
secrets: {}
