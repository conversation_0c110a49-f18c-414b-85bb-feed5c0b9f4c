"use client";

import { createContext, useContext, useState } from "react";

// Function to update question numbers to be continuous across sections
const updateQuestionNumbers = (questionsArray) => {
  let currentNumber = 1;

  return questionsArray.map((question) => {
    // Skip section headers
    if (question.type === "section_start") {
      return question;
    }

    // Format the number with leading zero if needed
    const formattedNumber =
      currentNumber < 10 ? `0${currentNumber}` : `${currentNumber}`;

    // Create a new question object with updated number
    const updatedQuestion = {
      ...question,
      en: {
        ...question.en,
        number: formattedNumber,
      },
      hi: {
        ...question.hi,
        number: formattedNumber,
      },
    };

    // Increment the counter for the next question
    currentNumber++;

    return updatedQuestion;
  });
};

// Define the sections and questions with translations
const commonOptions = {
  en: [
    { value: 1, label: "Disagree strongly" },
    { value: 2, label: "Disagree a little" },
    { value: 3, label: "Neither agree nor disagree" },
    { value: 4, label: "Agree a little" },
    { value: 5, label: "Agree strongly" },
  ],
  hi: [
    { value: 1, label: "पूरी तरह असहमत" },
    { value: 2, label: "थोड़ा असहमत" },
    { value: 3, label: "न सहमत न असहमत" },
    { value: 4, label: "थोड़ा सहमत" },
    { value: 5, label: "पूरी तरह सहमत" },
  ],
};

const pssOptions = {
  en: [
    { value: 0, label: "Never" },
    { value: 1, label: "Almost never" },
    { value: 2, label: "Sometimes" },
    { value: 3, label: "Fairly often" },
    { value: 4, label: "Very often" },
  ],
  hi: [
    { value: 0, label: "कभी नहीं" },
    { value: 1, label: "लगभग कभी नहीं" },
    { value: 2, label: "कभी-कभी" },
    { value: 3, label: "काफी अक्सर" },
    { value: 4, label: "बहुत अक्सर" },
  ],
};

const cbicQuestions = [
  {
    id: "cbic_1",
    isReversed: false,
    category: "Empathy",
    en: {
      number: "01",
      image: "/c1.svg",
      text: "It’s better to guide someone to the rules than explain everything myself.",
      options: commonOptions.en,
    },
    hi: {
      number: "01",
      text: "सब कुछ खुद समझाने के बजाय किसी को नियमों का मार्गदर्शन करना बेहतर है।",
      options: commonOptions.hi,
    },
  },
  {
    id: "cbic_2",
    isReversed: false,
    category: "Empathy",
    en: {
      number: "02",
      image: "/c2.svg",
      text: "Helping one person too long can delay other work.",
      options: commonOptions.en,
    },
    hi: {
      number: "02",
      text: "एक व्यक्ति की बहुत देर तक मदद करने से अन्य काम में देरी हो सकती है।",
      options: commonOptions.hi,
    },
  },
  {
    id: "cbic_3",
    isReversed: false,
    category: "Emotional",
    en: {
      number: "03",
      image: "/c3.png",
      text: "Staying calm feels hard when someone talks rudely.",
      options: commonOptions.en,
    },
    hi: {
      number: "03",
      text: "जब कोई असभ्य तरीके से बात करता है तो शांत रहना मुश्किल लगता है।",
      options: commonOptions.hi,
    },
  },
  {
    id: "cbic_4",
    isReversed: false,
    category: "Empathy",
    en: {
      number: "04",
      image: "/c4.svg",
      text: "If many people ask the same thing, the process might be confusing.",
      options: commonOptions.en,
    },
    hi: {
      number: "04",
      text: "यदि कई लोग एक ही बात पूछते हैं, तो प्रक्रिया भ्रमित करने वाली हो सकती है।",
      options: commonOptions.hi,
    },
  },
  {
    id: "cbic_5",
    isReversed: false,
    category: "Decision",
    en: {
      number: "05",
      image: "/c5.svg",
      text: "If someone says they heard it differently, I avoid correcting them.",
      options: commonOptions.en,
    },
    hi: {
      number: "05",
      text: "यदि कोई कहता है कि उन्होंने इसे अलग तरीके से सुना है, तो मैं उन्हें ठीक करने से बचता हूं।",
      options: commonOptions.hi,
    },
  },
  {
    id: "cbic_6",
    isReversed: false,
    category: "Empathy",
    en: {
      number: "06",
      image: "/c6.svg",
      text: "People who aren’t prepared should be reminded to be more responsible.",
      options: commonOptions.en,
    },
    hi: {
      number: "06",
      text: "जो लोग तैयार नहीं हैं, उन्हें अधिक जिम्मेदार होने की याद दिलाई जानी चाहिए।",
      options: commonOptions.hi,
    },
  },
  {
    id: "cbic_7",
    isReversed: false,
    category: "Emotional",
    en: {
      number: "07",
      image: "/c7.png",
      text: "Some doubts don’t need full answers — a short reply works.",
      options: commonOptions.en,
    },
    hi: {
      number: "07",
      text: "कुछ संदेहों को पूरे उत्तर की आवश्यकता नहीं होती है - एक छोटा जवाब काम करता है।",
      options: commonOptions.hi,
    },
  },
  {
    id: "cbic_8",
    isReversed: false,
    category: "Emotional",
    en: {
      number: "08",
      image: "/c8.png",
      text: "I stay quiet when someone is upset and wait for them to calm down.",
      options: commonOptions.en,
    },
    hi: {
      number: "08",
      text: "जब कोई परेशान होता है तो मैं चुप रहता हूं और उनके शांत होने का इंतजार करता हूं।",
      options: commonOptions.hi,
    },
  },
  {
    id: "cbic_9",
    isReversed: false,
    category: "Decision",
    en: {
      number: "09",
      image: "/c9.svg",
      text: "Explaining too much can make things more confusing.",
      options: commonOptions.en,
    },
    hi: {
      number: "09",
      text: "बहुत अधिक समझाने से चीजें और भ्रमित हो सकती हैं।",
      options: commonOptions.hi,
    },
  },
  {
    id: "cbic_10",
    isReversed: false,
    category: "Decision",
    en: {
      number: "10",
      image: "/c10.svg",
      text: "When I’m tired, I choose which things to reply to and leave the rest.",
      options: commonOptions.en,
    },
    hi: {
      number: "10",
      text: "जब मैं थका हुआ होता हूं, तो मैं चुनता हूं कि किन चीजों का जवाब देना है और बाकी को छोड़ देता हूं।",
      options: commonOptions.hi,
    },
  },
];

const resilienceQuestions = [
  {
    id: "resilience_1",
    isReversed: false,
    category: "Resilience",
    en: {
      number: "01",
      image: "/r1.svg",
      text: "I am able to adapt when changes occur.",
      options: commonOptions.en,
    },
    hi: {
      number: "01",
      text: "मैं परिवर्तन होने पर अनुकूल होने में सक्षम हूं।",
      options: commonOptions.hi,
    },
  },
  {
    id: "resilience_2",
    isReversed: false,
    category: "Resilience",
    en: {
      number: "02",
      image: "/r2.svg",
      text: "When facing challenges, do you have someone you feel you can turn to?",
      options: commonOptions.en,
    },
    hi: {
      number: "02",
      text: "मेरा एक करीबी और सुरक्षित रिश्ता है।",
      options: commonOptions.hi,
    },
  },

  {
    id: "resilience_3",
    isReversed: false,
    category: "Resilience",
    en: {
      number: "03",
      image: "/r3.svg",
      text: "Sometimes fate or God helps me.",
      options: commonOptions.en,
    },
    hi: {
      number: "03",
      text: "कभी-कभी नियति या ईश्वर मेरी मदद करते हैं।",
      options: commonOptions.hi,
    },
  },
  {
    id: "resilience_4",
    isReversed: false,
    category: "Resilience",
    en: {
      number: "04",
      image: "/r4.svg",
      text: "I can deal with whatever comes my way.",
      options: commonOptions.en,
    },
    hi: {
      number: "04",
      text: "मैं जो भी मेरे रास्ते में आता है उससे निपट सकता/सकती हूं।",
      options: commonOptions.hi,
    },
  },
  {
    id: "resilience_5",
    isReversed: false,
    category: "Resilience",
    en: {
      number: "05",
      image: "/r5.svg",
      text: "Past successes give me confidence.",
      options: commonOptions.en,
    },
    hi: {
      number: "05",
      text: "पिछली सफलताएं मुझे आत्मविश्वास देती हैं।",
      options: commonOptions.hi,
    },
  },
  {
    id: "resilience_6",
    isReversed: false,
    category: "Resilience",
    en: {
      number: "06",
      image: "/r6.png",
      text: "I try to see the humorous side of things when I am faced with problems.",
      options: commonOptions.en,
    },
    hi: {
      number: "06",
      text: "जब मैं समस्याओं का सामना करता/करती हूं, तो मैं चीजों के हास्यपूर्ण पहलू को देखने की कोशिश करता/करती हूं।",
      options: commonOptions.hi,
    },
  },
  {
    id: "resilience_7",
    isReversed: false,
    category: "Resilience",
    en: {
      number: "07",
      image: "/r7.png",
      text: "Having to cope with stress can make me stronger.",
      options: commonOptions.en,
    },
    hi: {
      number: "07",
      text: "तनाव से निपटना मुझे मजबूत बना सकता है।",
      options: commonOptions.hi,
    },
  },
  {
    id: "resilience_8",
    isReversed: false,
    category: "Resilience",
    en: {
      number: "08",
      image: "/r8.svg",
      text: "I tend to bounce back after illness, injury or other hardships.",
      options: commonOptions.en,
    },
    hi: {
      number: "08",
      text: "बीमारी, चोट या अन्य कठिनाइयों के बाद मैं वापस उभरने की प्रवृत्ति रखता/रखती हूं।",
      options: commonOptions.hi,
    },
  },
  {
    id: "resilience_9",
    isReversed: false,
    category: "Resilience",
    en: {
      number: "09",
      image: "/r9.svg",
      text: "I believe most things happen for a reason.",
      options: commonOptions.en,
    },
    hi: {
      number: "09",
      text: "मेरा मानना है कि अधिकांश चीजें किसी कारण से होती हैं।",
      options: commonOptions.hi,
    },
  },
  {
    id: "resilience_10",
    isReversed: false,
    category: "Resilience",
    en: {
      number: "10",
      image: "/r10.svg",
      text: "I make my best effort, no matter what.",
      options: commonOptions.en,
    },
    hi: {
      number: "10",
      text: "मैं चाहे जो भी हो, अपना सर्वश्रेष्ठ प्रयास करता/करती हूं।",
      options: commonOptions.hi,
    },
  },
  {
    id: "resilience_11",
    isReversed: false,
    category: "Resilience",
    en: {
      number: "11",
      image: "/r11.svg",
      text: "I believe I can achieve my goals, even if there are obstacles.",
      options: commonOptions.en,
    },
    hi: {
      number: "11",
      text: "मेरा मानना है कि मैं अपने लक्ष्यों को प्राप्त कर सकता/सकती हूं, भले ही बाधाएं हों।",
      options: commonOptions.hi,
    },
  },
  {
    id: "resilience_12",
    isReversed: false,
    category: "Resilience",
    en: {
      number: "12",
      image: "/r12.svg",
      text: "Even when hopeless, I do not give up.",
      options: commonOptions.en,
    },
    hi: {
      number: "12",
      text: "भले ही निराशाजनक स्थिति हो, मैं हार नहीं मानता/मानती।",
      options: commonOptions.hi,
    },
  },
  {
    id: "resilience_13",
    isReversed: false,
    category: "Resilience",
    en: {
      number: "13",
      image: "/r13.svg",
      text: "In times of stress, I know where to find help.",
      options: commonOptions.en,
    },
    hi: {
      number: "13",
      text: "तनाव के समय में, मुझे पता है कि मदद कहां से प्राप्त करनी है।",
      options: commonOptions.hi,
    },
  },
  {
    id: "resilience_14",
    isReversed: false,
    category: "Resilience",
    en: {
      number: "14",
      image: "/r14.svg",
      text: "Under pressure, I stay focused and think clearly.",
      options: commonOptions.en,
    },
    hi: {
      number: "14",
      text: "दबाव में, मैं केंद्रित रहता/रहती हूं और स्पष्ट रूप से सोचता/सोचती हूं।",
      options: commonOptions.hi,
    },
  },
  {
    id: "resilience_15",
    isReversed: false,
    category: "Resilience",
    en: {
      number: "15",
      image: "/r15.svg",
      text: "I prefer to take the lead in problem-solving.",
      options: commonOptions.en,
    },
    hi: {
      number: "15",
      text: "मैं समस्या-समाधान में नेतृत्व लेना पसंद करता/करती हूं।",
      options: commonOptions.hi,
    },
  },
  {
    id: "resilience_16",
    isReversed: false,
    category: "Resilience",
    en: {
      number: "16",
      image: "/r16.png",
      text: "I am not easily discouraged by failure.",
      options: commonOptions.en,
    },
    hi: {
      number: "16",
      text: "मैं विफलता से आसानी से निराश नहीं होता/होती।",
      options: commonOptions.hi,
    },
  },
  {
    id: "resilience_17",
    isReversed: false,
    category: "Resilience",
    en: {
      number: "17",
      image: "/r17.svg",
      text: "I think of myself as a strong person when dealing with life's challenges and difficulties.",
      options: commonOptions.en,
    },
    hi: {
      number: "17",
      text: "जीवन की चुनौतियों और कठिनाइयों से निपटते समय मैं अपने आप को एक मजबूत व्यक्ति के रूप में देखता/देखती हूं।",
      options: commonOptions.hi,
    },
  },
  {
    id: "resilience_18",
    isReversed: false,
    category: "Resilience",
    en: {
      number: "18",
      image: "/r18.svg",
      text: "I make unpopular or difficult decisions.",
      options: commonOptions.en,
    },
    hi: {
      number: "18",
      text: "मैं अलोकप्रिय या कठिन निर्णय लेता/लेती हूं।",
      options: commonOptions.hi,
    },
  },
  {
    id: "resilience_19",
    isReversed: false,
    category: "Resilience",
    en: {
      number: "19",
      image: "/r19.svg",
      text: "I am able to handle unpleasant or painful feelings like sadness, fear, and anger.",
      options: commonOptions.en,
    },
    hi: {
      number: "19",
      text: "मैं उदासी, भय और क्रोध जैसी अप्रिय या दर्दनाक भावनाओं को संभालने में सक्षम हूं।",
      options: commonOptions.hi,
    },
  },
  {
    id: "resilience_20",
    isReversed: false,
    category: "Resilience",
    en: {
      number: "20",
      image: "/r20.svg",
      text: "I have to act on a hunch.",
      options: commonOptions.en,
    },
    hi: {
      number: "20",
      text: "मुझे अपनी अंतर्ज्ञान पर कार्य करना पड़ता है।",
      options: commonOptions.hi,
    },
  },
  {
    id: "resilience_21",
    isReversed: false,
    category: "Resilience",
    en: {
      number: "21",
      image: "/r21.svg",
      text: "I have a strong sense of purpose in life.",
      options: commonOptions.en,
    },
    hi: {
      number: "21",
      text: "मेरे जीवन में एक मजबूत उद्देश्य की भावना है।",
      options: commonOptions.hi,
    },
  },
  {
    id: "resilience_22",
    isReversed: false,
    category: "Resilience",
    en: {
      number: "22",
      image: "/r22.svg",
      text: "I feel like I am in control.",
      options: commonOptions.en,
    },
    hi: {
      number: "22",
      text: "मुझे लगता है कि मैं नियंत्रण में हूं।",
      options: commonOptions.hi,
    },
  },
  {
    id: "resilience_23",
    isReversed: false,
    category: "Resilience",
    en: {
      number: "23",
      image: "/r23.svg",
      text: "I like challenges.",
      options: commonOptions.en,
    },
    hi: {
      number: "23",
      text: "मुझे चुनौतियां पसंद हैं।",
      options: commonOptions.hi,
    },
  },
  {
    id: "resilience_24",
    isReversed: false,
    category: "Resilience",
    en: {
      number: "24",
      image: "/r24.svg",
      text: "I work to attain goals.",
      options: commonOptions.en,
    },
    hi: {
      number: "24",
      text: "मैं लक्ष्यों को प्राप्त करने के लिए काम करता/करती हूं।",
      options: commonOptions.hi,
    },
  },
  {
    id: "resilience_25",
    isReversed: false,
    category: "Resilience",
    en: {
      image: "/r25.svg",
      number: "25",
      text: "I take pride in my achievements.",
      options: commonOptions.en,
    },
    hi: {
      number: "25",
      text: "मैं अपनी उपलब्धियों पर गर्व करता/करती हूं।",
      options: commonOptions.hi,
    },
  },
];

const decisionStyleQuestions = [
  {
    id: "decision_1",
    isReversed: false,
    category: "Rational",
    en: {
      number: "01",
      image: "/d1.svg",
      text: "I prefer to gather all the necessary information before committing to a decision.",
      options: commonOptions.en,
    },
    hi: {
      number: "01",
      text: "मैं किसी निर्णय पर प्रतिबद्ध होने से पहले सभी आवश्यक जानकारी इकट्ठा करना पसंद करता/करती हूं।",
      options: commonOptions.hi,
    },
  },
  {
    id: "decision_2",
    isReversed: false,
    category: "Rational",
    en: {
      number: "02",
      image: "/d2.svg",
      text: "I thoroughly evaluate decision alternatives before making a final choice.",
      options: commonOptions.en,
    },
    hi: {
      number: "02",
      text: "मैं अंतिम विकल्प चुनने से पहले निर्णय विकल्पों का पूरी तरह से मूल्यांकन करता/करती हूं।",
      options: commonOptions.hi,
    },
  },
  {
    id: "decision_3",
    isReversed: false,
    category: "Rational",
    en: {
      number: "03",
      image: "/d3.svg",
      text: "In decision making, I take time to contemplate the pros/cons or risks/benefits of a situation.",
      options: commonOptions.en,
    },
    hi: {
      number: "03",
      text: "निर्णय लेने में, मैं किसी स्थिति के फायदे/नुकसान या जोखिम/लाभ पर विचार करने के लिए समय लेता/लेती हूं।",
      options: commonOptions.hi,
    },
  },
  {
    id: "decision_4",
    isReversed: false,
    category: "Rational",
    en: {
      number: "04",
      image: "/d4.svg",

      text: "Investigating the facts is an important part of my decision making process.",
      options: commonOptions.en,
    },
    hi: {
      number: "04",
      text: "तथ्यों की जांच करना मेरी निर्णय लेने की प्रक्रिया का एक महत्वपूर्ण हिस्सा है।",
      options: commonOptions.hi,
    },
  },
  {
    id: "decision_5",
    isReversed: false,
    category: "Rational",
    en: {
      number: "05",
      image: "/d5.svg",
      text: "I weigh a number of different factors when making decisions.",
      options: commonOptions.en,
    },
    hi: {
      number: "05",
      text: "निर्णय लेते समय मैं कई अलग-अलग कारकों पर विचार करता/करती हूं।",
      options: commonOptions.hi,
    },
  },
  {
    id: "decision_6",
    isReversed: false,
    category: "Intuitive",
    en: {
      number: "06",
      image: "/d6.svg",
      text: "When making decisions, I rely mainly on my gut feelings.",
      options: commonOptions.en,
    },
    hi: {
      number: "06",
      text: "निर्णय लेते समय, मैं मुख्य रूप से अपनी अंतर्ज्ञान पर भरोसा करता/करती हूं।",
      options: commonOptions.hi,
    },
  },
  {
    id: "decision_7",
    isReversed: false,
    category: "Intuitive",
    en: {
      number: "07",
      image: "/d7.svg",
      text: "My initial hunch about decisions is generally what I follow.",
      options: commonOptions.en,
    },
    hi: {
      number: "07",
      text: "निर्णयों के बारे में मेरा प्रारंभिक अनुमान आमतौर पर वही होता है जिसका मैं पालन करता/करती हूं।",
      options: commonOptions.hi,
    },
  },
  {
    id: "decision_8",
    isReversed: false,
    category: "Intuitive",
    en: {
      number: "08",
      image: "/d8.png",
      text: "I make decisions based on intuition.",
      options: commonOptions.en,
    },
    hi: {
      number: "08",
      text: "मैं अंतर्ज्ञान के आधार पर निर्णय लेता/लेती हूं।",
      options: commonOptions.hi,
    },
  },
  {
    id: "decision_9",
    isReversed: false,
    category: "Intuitive",
    en: {
      number: "09",
      image: "/d9.svg",
      text: "I rely on my first impressions when making decisions.",
      options: commonOptions.en,
    },
    hi: {
      number: "09",
      text: "निर्णय लेते समय मैं अपने पहले प्रभाव पर भरोसा करता/करती हूं।",
      options: commonOptions.hi,
    },
  },
  {
    id: "decision_10",
    isReversed: false,
    category: "Intuitive",
    en: {
      number: "10",
      image: "/d10.png",
      text: "I weigh feelings more than analysis in making decisions.",
      options: commonOptions.en,
    },
    hi: {
      number: "10",
      text: "निर्णय लेने में मैं विश्लेषण की तुलना में भावनाओं को अधिक महत्व देता/देती हूं।",
      options: commonOptions.hi,
    },
  },
];

const pssQuestions = [
  {
    id: "pss_1",
    isReversed: false,
    category: "Perceived Stress",
    en: {
      number: "01",
      image: "/ps1.svg",

      text: "In the last month, how often have you been upset because of something that happened unexpectedly?",
      options: pssOptions.en,
    },
    hi: {
      number: "01",
      text: "पिछले महीने में, आप कितनी बार किसी अप्रत्याशित घटना के कारण परेशान हुए हैं?",
      options: pssOptions.hi,
    },
  },
  {
    id: "pss_2",
    isReversed: false,
    category: "Perceived Stress",
    en: {
      number: "02",
      image: "/ps2.svg",

      text: "In the last month, how often have you felt that you were unable to control the important things in your life?",
      options: pssOptions.en,
    },
    hi: {
      number: "02",
      text: "पिछले महीने में, आपने कितनी बार महसूस किया है कि आप अपने जीवन की महत्वपूर्ण चीजों को नियंत्रित करने में असमर्थ थे?",
      options: pssOptions.hi,
    },
  },
  {
    id: "pss_3",
    isReversed: false,
    category: "Perceived Stress",
    en: {
      number: "03",
      image: "/ps3.svg",

      text: "In the last month, how often have you felt nervous and stressed?",
      options: pssOptions.en,
    },
    hi: {
      number: "03",
      text: "पिछले महीने में, आपने कितनी बार घबराहट और तनाव महसूस किया है?",
      options: pssOptions.hi,
    },
  },
  {
    id: "pss_4",
    isReversed: true,
    category: "Perceived Stress",
    en: {
      number: "04",
      image: "/ps4.svg",

      text: "In the last month, how often have you felt confident about your ability to handle your personal problems?",
      options: pssOptions.en,
    },
    hi: {
      number: "04",
      text: "पिछले महीने में, आपने कितनी बार अपनी व्यक्तिगत समस्याओं को संभालने की अपनी क्षमता के बारे में आत्मविश्वास महसूस किया है?",
      options: pssOptions.hi,
    },
  },
  {
    id: "pss_5",
    isReversed: true,
    category: "Perceived Stress",
    en: {
      number: "05",
      image: "/ps5.svg",

      text: "In the last month, how often have you felt that things were going your way?",
      options: pssOptions.en,
    },
    hi: {
      number: "05",
      text: "पिछले महीने में, आपने कितनी बार महसूस किया है कि चीजें आपके अनुकूल हो रही हैं?",
      options: pssOptions.hi,
    },
  },
  {
    id: "pss_6",
    isReversed: false,
    category: "Perceived Stress",
    en: {
      number: "06",
      image: "/ps6.svg",

      text: "In the last month, how often have you found that you could not cope with all the things that you had to do?",
      options: pssOptions.en,
    },
    hi: {
      number: "06",
      text: "पिछले महीने में, आपने कितनी बार पाया है कि आप उन सभी चीजों से निपट नहीं सकते थे जो आपको करनी थीं?",
      options: pssOptions.hi,
    },
  },
  {
    id: "pss_7",
    isReversed: true,
    category: "Perceived Stress",
    en: {
      number: "07",
      image: "/ps7.svg",

      text: "In the last month, how often have you been able to control irritations in your life?",
      options: pssOptions.en,
    },
    hi: {
      number: "07",
      text: "पिछले महीने में, आप कितनी बार अपने जीवन में चिड़चिड़ापन को नियंत्रित करने में सक्षम रहे हैं?",
      options: pssOptions.hi,
    },
  },
  {
    id: "pss_8",
    isReversed: true,
    category: "Perceived Stress",
    en: {
      number: "08",
      image: "/ps8.svg",

      text: "In the last month, how often have you felt that you were on top of things?",
      options: pssOptions.en,
    },
    hi: {
      number: "08",
      text: "पिछले महीने में, आपने कितनी बार महसूस किया है कि आप चीजों पर नियंत्रण रखते हैं?",
      options: pssOptions.hi,
    },
  },
  {
    id: "pss_9",
    isReversed: false,
    category: "Perceived Stress",
    en: {
      number: "09",
      image: "/ps9.svg",
      text: "In the last month, how often have you been angered because of things that happened that were outside of your control?",
      options: pssOptions.en,
    },
    hi: {
      number: "09",
      text: "पिछले महीने में, आप कितनी बार उन चीजों के कारण क्रोधित हुए हैं जो आपके नियंत्रण से बाहर थीं?",
      options: pssOptions.hi,
    },
  },
  {
    id: "pss_10",
    isReversed: false,
    category: "Perceived Stress",
    en: {
      number: "10",
      image: "/ps10.svg",
      text: "In the last month, how often have you felt difficulties were piling up so high that you could not overcome them?",
      options: pssOptions.en,
    },
    hi: {
      number: "10",
      text: "पिछले महीने में, आपने कितनी बार महसूस किया है कि कठिनाइयां इतनी अधिक हो गई हैं कि आप उन्हें पार नहीं कर सकते?",
      options: pssOptions.hi,
    },
  },
];

const bfiQuestions = [
  {
    id: "bfi_1",
    isReversed: true,
    category: "Extraversion",
    en: {
      number: "01",
      image: "/bf1.svg",
      text: "I see myself as someone who is reserved",
      options: commonOptions.en,
    },
    hi: {
      number: "01",
      image: "/bf1.svg",

      text: "मैं खुद को एक संकोची व्यक्ति के रूप में देखता/देखती हूं",
      options: commonOptions.hi,
    },
  },
  {
    id: "bfi_2",
    isReversed: false,
    category: "Agreeableness",
    en: {
      number: "02",
      image: "/bf2.svg",
      text: "I see myself as someone who is generally trusting",
      options: commonOptions.en,
    },
    hi: {
      number: "02",
      image: "/bf2.svg",

      text: "मैं खुद को एक आमतौर पर विश्वास करने वाले व्यक्ति के रूप में देखता/देखती हूं",
      options: commonOptions.hi,
    },
  },
  {
    id: "bfi_3",
    isReversed: true,
    category: "Conscientiousness",
    en: {
      number: "03",
      image: "/bf3.svg",

      text: "I see myself as someone who tends to be lazy",
      options: commonOptions.en,
    },
    hi: {
      number: "03",
      image: "/bf3.svg",

      text: "मैं खुद को एक आलसी होने की प्रवृत्ति वाले व्यक्ति के रूप में देखता/देखती हूं",
      options: commonOptions.hi,
    },
  },
  {
    id: "bfi_4",
    isReversed: true,
    category: "Neuroticism",
    en: {
      number: "04",
      image: "/bf4.svg",
      text: "I see myself as someone who is relaxed, handles stress well",
      options: commonOptions.en,
    },
    hi: {
      number: "04",
      image: "/bf4.svg",
      text: "मैं खुद को एक शांत और तनाव को अच्छी तरह से संभालने वाले व्यक्ति के रूप में देखता/देखती हूं",
      options: commonOptions.hi,
    },
  },
  {
    id: "bfi_5",
    isReversed: true,
    category: "Openness",
    en: {
      number: "05",
      image: "/bf5.svg",
      image: "/bf5.svg",
      text: "I see myself as someone who has few artistic interests",
      options: commonOptions.en,
    },
    hi: {
      number: "05",
      image: "/bf5.svg",
      text: "मैं खुद को कम कलात्मक रुचियों वाले व्यक्ति के रूप में देखता/देखती हूं",
      options: commonOptions.hi,
    },
  },
  {
    id: "bfi_6",
    isReversed: false,
    category: "Extraversion",
    en: {
      number: "06",
      image: "/bf6.svg",
      text: "I see myself as someone who is outgoing, sociable",
      options: commonOptions.en,
    },
    hi: {
      number: "06",
      image: "/bf6.svg",
      text: "मैं खुद को एक बहिर्मुखी, मिलनसार व्यक्ति के रूप में देखता/देखती हूं",
      options: commonOptions.hi,
    },
  },
  {
    id: "bfi_7",
    isReversed: true,
    category: "Agreeableness",
    en: {
      number: "07",
      image: "/bf7.svg",
      text: "I see myself as someone who tends to find fault with others",
      options: commonOptions.en,
    },
    hi: {
      number: "07",
      text: "मैं खुद को दूसरों में दोष ढूंढने की प्रवृत्ति वाले व्यक्ति के रूप में देखता/देखती हूं",
      options: commonOptions.hi,
    },
  },
  {
    id: "bfi_8",
    isReversed: false,
    category: "Conscientiousness",
    en: {
      number: "08",
      image: "/bf8.svg",

      text: "I see myself as someone who does a thorough job",
      options: commonOptions.en,
    },
    hi: {
      number: "08",
      text: "मैं खुद को काम को पूरी तरह से करने वाले व्यक्ति के रूप में देखता/देखती हूं",
      options: commonOptions.hi,
    },
  },
  {
    id: "bfi_9",
    isReversed: false,
    category: "Neuroticism",
    en: {
      number: "09",
      image: "/bf9.svg",

      text: "I see myself as someone who gets nervous easily",
      options: commonOptions.en,
    },
    hi: {
      number: "09",
      text: "मैं खुद को आसानी से घबरा जाने वाले व्यक्ति के रूप में देखता/देखती हूं",
      options: commonOptions.hi,
    },
  },
  {
    id: "bfi_10",
    isReversed: false,
    category: "Openness",
    en: {
      number: "10",
      image: "/bf10.svg",

      text: "I see myself as someone who has an active imagination",
      options: commonOptions.en,
    },
    hi: {
      number: "10",
      text: "मैं खुद को सक्रिय कल्पना शक्ति वाले व्यक्ति के रूप में देखता/देखती हूं",
      options: commonOptions.hi,
    },
  },
];

// Define the base questions array
const baseQuestions = [
  {
    id: "section1",
    type: "section_start",
    en: {
      title: "Let's get started",
      // subtitle: "Answer each question at your own pace.",
      // image: "/begin.svg",
      // subtitle: "An opportunity to reflect on your skills and potential.",
      // image: "/begin.svg",
    },
    hi: {
      title: "आइए शुरू करें",
      // subtitle: "प्रत्येक प्रश्न का उत्तर अपनी गति से दें।",
      // image: "/begin.svg",
    },
  },
  ...bfiQuestions,
  {
    id: "section2",
    type: "section_start",
    en: {
      title: "Great job, continue",
      // subtitle: "You're making excellent progress!",
      // image: "/personality.svg",
    },
    hi: {
      title: "शानदार काम, जारी रखें",
      subtitle: "आप बहुत अच्छी प्रगति कर रहे हैं!",
      image: "/personality.svg",
    },
  },
  ...cbicQuestions,

  {
    id: "section3",
    type: "section_start",
    en: {
      title: "You're doing great",
      // subtitle: "Keep up the good work!",
      // image: "/personality.svg",
    },
    hi: {
      title: "आप बहुत अच्छा कर रहे हैं",
      subtitle: "अच्छा काम जारी रखें!",
      image: "/personality.svg",
    },
  },
  ...pssQuestions,

  {
    id: "section4",
    type: "section_start",
    en: {
      title: "Almost there!",
      // subtitle: "Just a few more questions to go.",
      // image: "/resilience.svg",
    },
    hi: {
      title: "बस थोड़ा और!",
      subtitle: "बस कुछ और प्रश्न बाकी हैं।",
      image: "/resilience.svg",
    },
  },
  ...decisionStyleQuestions,

  {
    id: "section5",
    type: "section_start",
    en: {
      title: "Almost at end",
      // subtitle: "Final set of questions.",
      // image: "/resilience.svg",
    },
    hi: {
      title: "लगभग अंत में",
      subtitle: "प्रश्नों का अंतिम सेट।",
      image: "/resilience.svg",
    },
  },
  ...resilienceQuestions,
];

// Apply continuous numbering to questions
const questions = updateQuestionNumbers(baseQuestions);

const QuestionnaireContext = createContext();

export function QuestionnaireProvider({ children }) {
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState([]);
  const [scores, setScores] = useState({
    personality: {
      extraversion: 0,
      agreeableness: 0,
      conscientiousness: 0,
      neuroticism: 0,
      openness: 0,
    },
    stress: 0,
    decisionStyle: {
      rational: 0,
      intuitive: 0,
    },
    resilience: 0,
    taxpayerJudgement: 0, // Added back for storing the average CBIC score
    cbic: {
      empathy: 0,
      emotional: 0,
      decision: 0,
    },
  });
  const totalQuestions = questions.length;

  // Calculate scores based on answers
  const calculateScores = (allAnswers) => {
    const newScores = {
      personality: {
        extraversion: 0,
        agreeableness: 0,
        conscientiousness: 0,
        neuroticism: 0,
        openness: 0,
      },
      stress: 0,
      decisionStyle: {
        rational: 0,
        intuitive: 0,
      },
      resilience: 0,
      taxpayerJudgement: 0, // Added back for storing the average CBIC score
      cbic: {
        empathy: 0,
        emotional: 0,
        decision: 0,
      },
    };

    // Process each answer
    allAnswers.forEach((answer) => {
      if (!answer) return;

      const { questionId, value } = answer;

      // Find the question
      const questionObj = [
        ...bfiQuestions,
        ...pssQuestions,
        ...decisionStyleQuestions,
        ...resilienceQuestions,
        ...cbicQuestions,
      ].find((q) => q.id === questionId);

      if (!questionObj) return;

      // Calculate score based on question type
      if (questionId.startsWith("bfi_")) {
        // BFI-10 scoring
        const trait = questionObj.category;
        const score = questionObj.isReversed ? 6 - value : value;

        const traitLower = trait.toLowerCase();
        if (traitLower in newScores.personality) {
          newScores.personality[traitLower] += score;
        } else {
          console.log(`Trait not found: ${trait} (${traitLower})`);
        }
      } else if (questionId.startsWith("pss_")) {
        // PSS-10 scoring
        const score = questionObj.isReversed ? 4 - value : value;
        newScores.stress += score;
      } else if (questionId.startsWith("decision_")) {
        // Decision Style scoring
        const style = questionObj.category;
        if (style === "Rational") {
          newScores.decisionStyle.rational += value;
        } else if (style === "Intuitive") {
          newScores.decisionStyle.intuitive += value;
        }
      } else if (questionId.startsWith("resilience_")) {
        // CD-RISC-25 scoring
        newScores.resilience += value;
      } else if (questionId.startsWith("cbic_")) {
        // CBIC Officer Assessment scoring
        const category = questionObj.category;
        if (category === "Empathy") {
          newScores.cbic.empathy += value;
        } else if (category === "Emotional") {
          newScores.cbic.emotional += value;
        } else if (category === "Decision") {
          newScores.cbic.decision += value;
        }
      }
    });

    // Calculate taxpayerJudgement as the average of CBIC dimensions
    // Count how many questions were answered in each category
    const empathyQuestions = allAnswers.filter(
      (a) =>
        a &&
        a.questionId.startsWith("cbic_") &&
        cbicQuestions.find((q) => q.id === a.questionId)?.category === "Empathy"
    ).length;

    const emotionalQuestions = allAnswers.filter(
      (a) =>
        a &&
        a.questionId.startsWith("cbic_") &&
        cbicQuestions.find((q) => q.id === a.questionId)?.category ===
          "Emotional"
    ).length;

    const decisionQuestions = allAnswers.filter(
      (a) =>
        a &&
        a.questionId.startsWith("cbic_") &&
        cbicQuestions.find((q) => q.id === a.questionId)?.category ===
          "Decision"
    ).length;

    // Calculate averages for each dimension
    const empathyAvg =
      empathyQuestions > 0 ? newScores.cbic.empathy / empathyQuestions : 0;
    const emotionalAvg =
      emotionalQuestions > 0
        ? newScores.cbic.emotional / emotionalQuestions
        : 0;
    const decisionAvg =
      decisionQuestions > 0 ? newScores.cbic.decision / decisionQuestions : 0;

    // Calculate overall average
    const totalDimensions =
      (empathyQuestions > 0 ? 1 : 0) +
      (emotionalQuestions > 0 ? 1 : 0) +
      (decisionQuestions > 0 ? 1 : 0);
    if (totalDimensions > 0) {
      newScores.taxpayerJudgement = Math.round(
        (empathyAvg + emotionalAvg + decisionAvg) / totalDimensions
      );
    }
    return newScores;
  };

  const handleAnswer = (questionId, value) => {
    // Save the answer
    const newAnswers = [...answers];
    newAnswers[currentQuestion] = { questionId, value };
    setAnswers(newAnswers);

    // Calculate scores
    const newScores = calculateScores(newAnswers);
    setScores(newScores);

    // Move to next question
    if (currentQuestion < totalQuestions - 1) {
      setCurrentQuestion(currentQuestion + 1);
    } else {
      // Handle completion of questionnaire
      // You can add logic to submit answers or show results
    }
  };

  const resetQuestionnaire = () => {
    setCurrentQuestion(0);
    setAnswers([]);
    setScores({
      personality: {
        extraversion: 0,
        agreeableness: 0,
        conscientiousness: 0,
        neuroticism: 0,
        openness: 0,
      },
      stress: 0,
      decisionStyle: {
        rational: 0,
        intuitive: 0,
      },
      resilience: 0,
      taxpayerJudgement: 0, // Added back for storing the average CBIC score
      cbic: {
        empathy: 0,
        emotional: 0,
        decision: 0,
      },
    });
  };

  return (
    <QuestionnaireContext.Provider
      value={{
        questions,
        currentQuestion,
        setCurrentQuestion,
        answers,
        setAnswers,
        scores,
        handleAnswer,
        resetQuestionnaire,
        totalQuestions,
      }}
    >
      {children}
    </QuestionnaireContext.Provider>
  );
}

export function useQuestionnaire() {
  const context = useContext(QuestionnaireContext);
  if (!context) {
    // For static rendering, return a default context instead of throwing an error
    if (typeof window === "undefined") {
      return {
        questions: [],
        currentQuestion: 0,
        setCurrentQuestion: () => {},
        answers: [],
        setAnswers: () => {},
        scores: {},
        handleAnswer: () => {},
        resetQuestionnaire: () => {},
        totalQuestions: 0,
      };
    }
    throw new Error(
      "useQuestionnaire must be used within a QuestionnaireProvider"
    );
  }
  return context;
}
