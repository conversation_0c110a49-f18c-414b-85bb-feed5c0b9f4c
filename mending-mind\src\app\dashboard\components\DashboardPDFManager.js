"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/app/components/ui/button";
import { Download, FileText } from "lucide-react";
import PDFViewer from "@/app/components/PDFViewer";

const DashboardPDFManager = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  useEffect(() => {
    // Listen for PDF generation requests
    const handlePDFRequest = (event) => {
      const { user } = event.detail;
      setCurrentUser(user);
      setRefreshTrigger(prev => prev + 1);
    };

    window.addEventListener('generate-pdf', handlePDFRequest);
    return () => window.removeEventListener('generate-pdf', handlePDFRequest);
  }, []);
  const transformUserData = (user) => {
    let scores = null;
    let previousAttempt = null;

    // Check if user has completed second attempt
    const hasSecondAttempt = user.quizAttempts && user.quizAttempts.length >= 2;

    if (user.currentAttempt) {
      scores = user.currentAttempt;
      
      // For second attempt, always include the first attempt for comparison
      if (hasSecondAttempt && user.attemptNumber === 2) {
        previousAttempt = user.quizAttempts[0]; // First attempt
      } else if (user.quizAttempts && user.quizAttempts.length > 1) {
        const currentIndex = user.attemptNumber - 1;
        if (currentIndex > 0) {
          previousAttempt = user.quizAttempts[currentIndex - 1];
        }
      }
    } else if (user.quizAttempts && user.quizAttempts.length > 0) {
      scores = user.quizAttempts[user.quizAttempts.length - 1];
      // If user has completed second attempt, include first attempt
      if (hasSecondAttempt) {
        previousAttempt = user.quizAttempts[0]; // First attempt
      } else {
        previousAttempt = user.quizAttempts.length > 1 ? user.quizAttempts[user.quizAttempts.length - 2] : null;
      }
    }

    const userInfo = {
      name: user.name,
      age: user.age,
      gender: user.gender,
      emailId: user.email,
      contactNo: user.contact,
    };

    return { userInfo, scores, previousAttempt, hasSecondAttempt };
  };

  return (
    <>
      {children}
      
      {/* Single PDF Generator for all users */}
      {currentUser && (
        <div className="pdf-generator-container hidden">
          <PDFViewer
            userInfo={transformUserData(currentUser).userInfo}
            reportScores={transformUserData(currentUser).scores}
            reportScores2={transformUserData(currentUser).previousAttempt}
            refreshTrigger={refreshTrigger}
          />
        </div>
      )}
    </>
  );
};

// Simplified button component
const DashboardReportButton = ({ user, disabled = false }) => {
  const [isGenerating, setIsGenerating] = useState(false);

  const handleDownload = async () => {
    if (isGenerating) return;

    try {
      setIsGenerating(true);

      // Request PDF generation via event
      window.dispatchEvent(new CustomEvent('generate-pdf', { detail: { user } }));

      // Wait for PDF generation
      await new Promise(resolve => setTimeout(resolve, 3000));      if (typeof window !== "undefined" && window.pdfBlob) {
        const url = URL.createObjectURL(window.pdfBlob);
        const link = document.createElement("a");
        link.href = url;
        
        // Create descriptive filename based on attempt
        const hasSecondAttempt = user.quizAttempts && user.quizAttempts.length >= 2;
        let filename;
        if (hasSecondAttempt && user.attemptNumber === 2) {
          filename = `${user.name?.replace(/[^a-zA-Z0-9]/g, '_') || 'User'}_Complete_Assessment_Report_Both_Attempts_${new Date().toISOString().split('T')[0]}.pdf`;
        } else if (user.attemptNumber) {
          filename = `${user.name?.replace(/[^a-zA-Z0-9]/g, '_') || 'User'}_Assessment_Report_Attempt_${user.attemptNumber}_${new Date().toISOString().split('T')[0]}.pdf`;
        } else {
          filename = `${user.name?.replace(/[^a-zA-Z0-9]/g, '_') || 'User'}_Assessment_Report_${new Date().toISOString().split('T')[0]}.pdf`;
        }
        link.download = filename;
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        setTimeout(() => URL.revokeObjectURL(url), 100);
      } else {
        throw new Error("PDF generation failed");
      }
    } catch (error) {
      console.error("Error downloading PDF:", error);
      alert("Error downloading PDF. Please try again.");
    } finally {
      setIsGenerating(false);
    }
  };

  // Check if user has valid data
  const hasValidData = user.currentAttempt || (user.quizAttempts && user.quizAttempts.length > 0);

  if (!hasValidData) {
    return (
      <Button variant="outline" size="sm" disabled className="opacity-50">
        <FileText className="mr-1 h-3 w-3" />
        No Data
      </Button>
    );
  }
  const getButtonText = () => {
    if (user.attemptNumber && user.attemptNumber > 0) {
      // Check if user has completed second attempt
      const hasSecondAttempt = user.quizAttempts && user.quizAttempts.length >= 2;
      
      if (hasSecondAttempt && user.attemptNumber === 2) {
        return "Complete Report"; // Both attempts included
      }
      return user.attemptNumber === 1 ? "Report (1st)" : user.attemptNumber === 2 ? "Report (2nd)" : `Report (${user.attemptNumber})`;  
    }
    return "Report";
  };

  return (
    <Button
      onClick={handleDownload}
      disabled={isGenerating || disabled}
      variant="outline"
      size="sm"
      className="hover:bg-[#F0C93B]/10"
    >
      {isGenerating ? (
        <>
          <svg className="animate-spin -ml-1 mr-1 h-3 w-3" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
          </svg>
          Generating...
        </>
      ) : (
        <>
          <Download className="mr-1 h-3 w-3" />
          {getButtonText()}
        </>
      )}
    </Button>
  );
};

export { DashboardPDFManager, DashboardReportButton };
