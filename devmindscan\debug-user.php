<?php
// Simple debug page to check user status and reset if needed
require("php/common.php");

echo "<h1>User Status Debug</h1>";

if (isset($_SESSION['id'])) {
    $userId = $_SESSION['id'];
    $userData = fetchData("all", "users", "id='" . $userId . "'");
    
    echo "<h2>Current User Information:</h2>";
    echo "User ID: " . $userId . "<br>";
    echo "Name: " . (isset($userData['username']) ? $userData['username'] : 'N/A') . "<br>";
    echo "Phone: " . (isset($userData['phone']) ? $userData['phone'] : 'N/A') . "<br>";
    echo "Status: " . (isset($userData['status']) ? $userData['status'] : 'N/A') . " (0=incomplete, 1=complete)<br>";
    
    echo "<h2>Actions:</h2>";
    
    if (isset($userData['status'])) {
        if ($userData['status'] == '1') {
            echo '<a href="' . $webUrl . 'reports.php" class="btn btn-success">View Results</a> ';
            echo '<a href="?action=reset" class="btn btn-warning" onclick="return confirm(\'This will delete your previous results. Are you sure?\')">Reset & Retake Quiz</a><br>';
        } else {
            echo '<a href="' . $webUrl . 'question?qn=1" class="btn btn-primary">Continue Quiz</a> ';
            echo '<a href="?action=reset" class="btn btn-secondary" onclick="return confirm(\'This will reset your progress. Are you sure?\')">Start Over</a><br>';
        }
    }
    
    if (isset($_GET['action']) && $_GET['action'] == 'reset') {
        // Reset user status
        $updateResult = updateQuery("users", "status='0'", "id='" . $userId . "'");
        $deleteResult = deleteQuery("answers", "user_id='" . $userId . "'");
        
        if ($updateResult) {
            echo "<div style='color: green; margin: 10px 0;'>✅ Quiz reset successfully!</div>";
            echo '<a href="' . $webUrl . 'question?qn=1" class="btn btn-primary">Start Quiz</a><br>';
        } else {
            echo "<div style='color: red; margin: 10px 0;'>❌ Failed to reset quiz</div>";
        }
    }
    
} else {
    echo "No active session found.<br>";
    echo '<a href="login.php">Login</a><br>';
}

echo "<br><a href='index.php'>Back to Home</a>";
?>

<style>
.btn {
    display: inline-block;
    padding: 8px 16px;
    margin: 5px;
    background: #007cba;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    border: none;
    cursor: pointer;
}
.btn-success { background: #28a745; }
.btn-warning { background: #ffc107; color: #212529; }
.btn-secondary { background: #6c757d; }
.btn-primary { background: #007bff; }
</style>
