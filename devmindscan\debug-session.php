<?php
// Debug session issues
session_start();

echo "<h1>Session Debug Information</h1>";

echo "<h2>Session Status:</h2>";
echo "Session Status: " . session_status() . "<br>";
echo "Session ID: " . session_id() . "<br>";
echo "Session Name: " . session_name() . "<br>";

echo "<h2>Session Data:</h2>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<h2>Session Configuration:</h2>";
echo "Session Save Path: " . session_save_path() . "<br>";
echo "Session Cookie Lifetime: " . ini_get('session.cookie_lifetime') . "<br>";
echo "Session GC Maxlifetime: " . ini_get('session.gc_maxlifetime') . "<br>";
echo "Session Use Cookies: " . ini_get('session.use_cookies') . "<br>";

echo "<h2>Test Session:</h2>";
if (!isset($_SESSION['test'])) {
    $_SESSION['test'] = 'Session working at ' . date('Y-m-d H:i:s');
    echo "✅ Session test value set<br>";
} else {
    echo "✅ Session test value exists: " . $_SESSION['test'] . "<br>";
}

echo "<h2>Simulate Login:</h2>";
if (isset($_GET['login'])) {
    $_SESSION['id'] = 'TEST_USER_ID';
    echo "✅ Test session ID set<br>";
}

if (isset($_SESSION['id'])) {
    echo "✅ Session ID exists: " . $_SESSION['id'] . "<br>";
} else {
    echo "❌ No session ID found<br>";
    echo '<a href="?login=1">Click to simulate login</a><br>';
}

echo "<h2>Check Session Function:</h2>";
include("php/common.php");
$sessionCheck = checkSession();
echo "checkSession() result: " . $sessionCheck . "<br>";

echo "<h2>File Permissions:</h2>";
$sessionPath = session_save_path();
if (empty($sessionPath)) {
    $sessionPath = '/tmp';
}
echo "Session path: $sessionPath<br>";
echo "Path exists: " . (is_dir($sessionPath) ? "Yes" : "No") . "<br>";
echo "Path writable: " . (is_writable($sessionPath) ? "Yes" : "No") . "<br>";

echo "<h2>Cookie Information:</h2>";
echo "<pre>";
print_r($_COOKIE);
echo "</pre>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2 { color: #333; }
pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
</style>
