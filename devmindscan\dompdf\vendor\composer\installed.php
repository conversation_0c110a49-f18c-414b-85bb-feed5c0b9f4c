<?php return array(
    'root' => array(
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '1.0.0.0',
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'reference' => NULL,
        'name' => '__root__',
        'dev' => true,
    ),
    'versions' => array(
        '__root__' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '1.0.0.0',
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'reference' => NULL,
            'dev_requirement' => false,
        ),
        'dompdf/dompdf' => array(
            'pretty_version' => 'v2.0.3',
            'version' => '2.0.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dompdf/dompdf',
            'aliases' => array(),
            'reference' => 'e8d2d5e37e8b0b30f0732a011295ab80680d7e85',
            'dev_requirement' => false,
        ),
        'masterminds/html5' => array(
            'pretty_version' => '2.7.6',
            'version' => '2.7.6.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../masterminds/html5',
            'aliases' => array(),
            'reference' => '897eb517a343a2281f11bc5556d6548db7d93947',
            'dev_requirement' => false,
        ),
        'phenx/php-font-lib' => array(
            'pretty_version' => '0.5.4',
            'version' => '0.5.4.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phenx/php-font-lib',
            'aliases' => array(),
            'reference' => 'dd448ad1ce34c63d09baccd05415e361300c35b4',
            'dev_requirement' => false,
        ),
        'phenx/php-svg-lib' => array(
            'pretty_version' => '0.5.0',
            'version' => '0.5.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phenx/php-svg-lib',
            'aliases' => array(),
            'reference' => '76876c6cf3080bcb6f249d7d59705108166a6685',
            'dev_requirement' => false,
        ),
        'sabberworm/php-css-parser' => array(
            'pretty_version' => '8.4.0',
            'version' => '8.4.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sabberworm/php-css-parser',
            'aliases' => array(),
            'reference' => 'e41d2140031d533348b2192a83f02d8dd8a71d30',
            'dev_requirement' => false,
        ),
    ),
);
