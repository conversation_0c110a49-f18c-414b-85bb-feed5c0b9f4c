<?php
session_start();

echo "<h1>Simple Session Test</h1>";

echo "<h2>Session Info:</h2>";
echo "Session ID: " . session_id() . "<br>";
echo "Session Status: " . session_status() . "<br>";
echo "Session Save Path: " . session_save_path() . "<br>";

if (isset($_GET['set'])) {
    $_SESSION['test_id'] = 'TEST_USER_' . time();
    echo "✅ Session set: " . $_SESSION['test_id'] . "<br>";
    echo '<a href="simple-session-test.php">Check if session persists</a><br>';
} elseif (isset($_SESSION['test_id'])) {
    echo "✅ Session exists: " . $_SESSION['test_id'] . "<br>";
    echo '<a href="simple-session-test.php?clear=1">Clear session</a><br>';
} elseif (isset($_GET['clear'])) {
    session_destroy();
    echo "✅ Session cleared<br>";
    echo '<a href="simple-session-test.php?set=1">Set new session</a><br>';
} else {
    echo "❌ No session found<br>";
    echo '<a href="simple-session-test.php?set=1">Set session</a><br>';
}

echo "<h2>All Session Data:</h2>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<h2>Test checkSession() Function:</h2>";
include("php/common.php");
$result = checkSession();
echo "checkSession() result: " . $result . "<br>";

if (isset($_SESSION['id'])) {
    echo "✅ Session 'id' exists: " . $_SESSION['id'] . "<br>";
} else {
    echo "❌ No session 'id' found<br>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2 { color: #333; }
pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
a { display: inline-block; margin: 5px 0; padding: 5px 10px; background: #007cba; color: white; text-decoration: none; border-radius: 3px; }
</style>
