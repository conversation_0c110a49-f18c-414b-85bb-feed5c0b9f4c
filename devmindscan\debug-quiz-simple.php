<?php
// Simple quiz status check
require("php/common.php");

echo "<h1>Simple Quiz Status</h1>";

if (isset($_SESSION['id'])) {
    $userId = $_SESSION['id'];
    $userData = fetchData("all", "users", "id='" . $userId . "'");
    
    echo "<h2>User Information:</h2>";
    echo "User ID: " . $userId . "<br>";
    echo "Name: " . (isset($userData['username']) ? $userData['username'] : 'N/A') . "<br>";
    echo "Status: " . (isset($userData['status']) ? $userData['status'] : 'N/A') . " (0=incomplete, 1=complete)<br>";
    
    echo "<h2>URLs:</h2>";
    echo "Current WebURL: " . $webUrl . "<br>";
    echo "Reports URL: " . $webUrl . "reports<br>";
    echo "Question URL: " . $webUrl . "question?qn=1<br>";
    
    echo "<h2>Available Actions:</h2>";
    
    if (isset($userData['status'])) {
        if ($userData['status'] == '1') {
            echo "✅ Quiz completed<br>";
            echo '<a href="' . $webUrl . 'reports.php" class="btn btn-success">View Reports</a><br>';
            echo '<a href="?reset=1" class="btn btn-warning">Reset Quiz</a><br>';
        } else {
            echo "📝 Quiz in progress<br>";
            echo '<a href="' . $webUrl . 'question?qn=1" class="btn btn-primary">Continue Quiz</a><br>';
            echo '<a href="?complete=1" class="btn btn-info">Mark as Complete</a><br>';
        }
    }
    
    // Handle actions
    if (isset($_GET['reset'])) {
        $updateResult = updateQuery("users", "status='0'", "id='" . $userId . "'");
        $deleteResult = deleteQuery("answers", "user_id='" . $userId . "'");
        echo "<div style='color: green;'>Quiz reset completed. <a href='debug-quiz-simple.php'>Refresh</a></div>";
    }
    
    if (isset($_GET['complete'])) {
        $updateResult = updateQuery("users", "status='1'", "id='" . $userId . "'");
        echo "<div style='color: green;'>Status marked as complete. <a href='debug-quiz-simple.php'>Refresh</a></div>";
    }
    
    // Test redirect
    echo "<h2>Test Final Redirect:</h2>";
    echo '<form method="post" action="php/submitAnswer.php">';
    echo '<input type="hidden" name="id" value="Q26">';
    echo '<input type="hidden" name="option" value="1">';
    echo '<button type="submit" class="btn btn-danger">Simulate Q26 Submit (Will redirect to reports)</button>';
    echo '</form>';
    
} else {
    echo "No session found<br>";
    echo '<a href="login.php" class="btn">Login</a><br>';
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.btn {
    display: inline-block;
    padding: 8px 16px;
    margin: 5px;
    background: #007bff;
    color: white;
    text-decoration: none;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}
.btn-success { background: #28a745; }
.btn-warning { background: #ffc107; color: #000; }
.btn-info { background: #17a2b8; }
.btn-danger { background: #dc3545; }
</style>
