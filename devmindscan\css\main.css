html {
  scroll-behavior: smooth;
  overflow-x: hidden;
}

* {
  font-family: "Montserrat", sans-serif;
  font-optical-sizing: auto;
  font-weight: normal;
  font-style: normal;
  color: #221f20;
}

body::-webkit-scrollbar {
  width: .5em;
}

body::-webkit-scrollbar-track {
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
}

body::-webkit-scrollbar-thumb {
  background-color: #000;
  outline: 1px solid #000;
  border-radius: 8px;
}

.top-nav-collapse {
  background-color: #fff !important;
}

.navbar:not(.top-nav-collapse) {
  background: #fff !important;
}

li.nav-item.no-hover::after {
  display: none !important;
}

@media (max-width: 768px) {
  .navbar:not(.top-nav-collapse) {
    background: #fff !important;
  }

  .border-right {
    border-right: 0px !important;
  }
}

#intro .h6 {
  font-weight: 300;
  line-height: 1.7;
}

.site-bg-img {
  height: 100vh;
  min-height: 480px;
  max-height: 1080px;
}

.hm-gradient .site-bg-img {
  background: rgba(42, 27, 161, 0.7);
  background: linear-gradient(45deg, rgb(0 0 0 / 95%), rgb(0 4 3 / 70%) 100%);
}

@media (max-width: 450px) {
  #remember {
    margin-top: 110px !important;
  }

  .left-side {
    padding-top: unset !important;
  }

  .home-body {
    width: 90% !important;
  }

  .form-box {
    width: 100% !important;
    margin: 55px auto !important;
  }

  lottie-player {
    width: 75px !important;
    height: 75px !important;
    margin-top: 65px !important;
    margin-left: 10px !important;
  }

  lottie-player.cong {
    width: 150px !important;
    height: 150px !important;
    margin: 0px !important;
  }

  h1#title {
    font-size: 2rem !important;
    margin-top: 70px;
    margin-right: 20px !important;
  }

  #home {
    margin-top: 55px !important;
  }

  .margins {
    margin-right: 1rem;
    margin-left: 1rem;
  }

  .md-form input:not([type]):focus:not([readonly]),
  .md-form input[type="text"]:not(.browser-default):focus:not([readonly]),
  .md-form input[type="password"]:not(.browser-default):focus:not([readonly]),
  .md-form input[type="email"]:not(.browser-default):focus:not([readonly]),
  .md-form input[type="url"]:not(.browser-default):focus:not([readonly]),
  .md-form input[type="time"]:not(.browser-default):focus:not([readonly]),
  .md-form input[type="date"]:not(.browser-default):focus:not([readonly]),
  .md-form input[type="datetime"]:not(.browser-default):focus:not([readonly]),
  .md-form input[type="datetime-local"]:not(.browser-default):focus:not([readonly]),
  .md-form input[type="tel"]:not(.browser-default):focus:not([readonly]),
  .md-form input[type="number"]:not(.browser-default):focus:not([readonly]),
  .md-form input[type="search"]:not(.browser-default):focus:not([readonly]),
  .md-form input[type="phone"]:not(.browser-default):focus:not([readonly]),
  .md-form input[type="search-md"]:focus:not([readonly]),
  .md-form textarea.md-textarea:focus:not([readonly]) {
    box-shadow: none !important;
  }

}

#pricing ul li {
  font-size: 1.1em;
}

.navbar-brand img {
  width: 150px;
  height: auto;
}

li.nav-item::after {
  content: "";
  display: block;
  position: absolute;
  width: 0%;
  height: 2px;
  background: black;
  transition: all 0.5s ease;
  left: 50%;
  transform: translate(-50%, 0);
}

li.nav-item:hover:after {
  width: 100%;
}

h2 span::after {
  content: "";
  width: 50%;
  height: 2px;
  background: inherit;
  position: absolute;
  bottom: -10px;
  left: 24%;
}

.underline-sm::after {
  width: 25%;
  left: 37%;
}

.btn-outline-black::after,
.sliding-bg::after {
  content: "";
  width: 0%;
  height: 100%;
  display: block;
  background: black;
  left: 0px;
  top: 0px;
  position: absolute;
  transition: all 0.5s ease;
  z-index: 0;
}

.navbar-brand img {
  width: 150px;
  height: auto;
}

a.btn-outline-black:hover,
.sliding-bg::after {
  color: #fff !important;
}

.btn-outline-black:hover::after,
.sliding-bg::after {
  width: 100%;
}

blockquote {
  border: none;
  font-family: Georgia, "Times New Roman", Times, serif;
  margin-bottom: -30px;
  quotes: "\201C" "\201D" "\2018" "\2019";
}

blockquote h4:before {
  content: open-quote;
  font-weight: bold;
  font-size: 4rem;
  color: white;
}

blockquote h4:after {
  content: close-quote;
  font-weight: bold;
  font-size: 4rem;
  color: white;

}

@media (max-width: 767.98px) {}

#home {
  margin-top: 40px;
}

.login-form {
  border-radius: 32px;
}

button#submit-btn,
.custom-button {
  position: relative;
  /* width: 50px; */
  /* height: 50px; */
  border: 1px solid transparent;
  border-radius: 8px;
  background: rgb(255, 114, 116);
  background: linear-gradient(90deg, rgba(255, 114, 116, 1) 0%, rgba(255, 154, 61, 1) 100%);
  font-weight: 500;
  font-size: 14px;
  padding: 10px;
}

h1#title {
  font-size: 3rem;
  margin-top: 70px;
  margin-right: 50px;
}

.home-body {
  width: 80%;
  margin: 40px auto;
  /* box-shadow: 0px 1px 5px 5px lightgrey !important; */
  font-size: 18px;
  font-weight: 400;
  letter-spacing: 1px;
  padding: 15px;
  background: rgb(255 255 255 / 60%);
}

ul.ticked-list li:before {
  content: "ÃƒÂ¢Ã…â€œÃ¢â‚¬Å“";
  font-weight: 800;
  position: absolute;
  left: 0px;
  color: #014163;
}

#home {
  margin-top: 40px;
  background-image: url("../img/IMG_0129-HDR.png");
  background-size: cover;
  background-repeat: no-repeat;
  object-fit: fill;
  background-position: center;
}

.left-side {
  padding-top: 70px;
}

lottie-player {
  width: 150px;
  height: 150px;
  margin: 0px auto;
}

lottie-player.cong {
  width: 100px;
  height: 100px;
  margin: auto;
}

.form-box {
  position: relative;
  width: 85%;
  margin: 65px auto;
  background: #0CC0DF;
  border-radius: 18px;
  padding: 10px 35px;
  border-color: #0CC0DF;
  -webkit-box-shadow: 3px 2px 15px -1px rgb(115 115 115);
  -moz-box-shadow: 3px 2px 15px -1px rgba(115, 115, 115, 1);
  box-shadow: 3px 2px 15px -1px rgb(115 115 115);
}

.question {
  background: transparent;
  color: #0b2938;
  padding: 10px;
  border-radius: 8px;
}

.option {
  background: #fff;
  border: 1px solid lightgray;
  /* padding: 12px 45px; */
  border-radius: 32px;
  /* margin-bottom: 15px; */
  transition: all 0.3s ease;
  cursor: pointer;
}

.option:hover {
  /* -webkit-box-shadow: 0px 1px 5px 0px rgb(0 0 0 / 75%); */
  -moz-box-shadow: 0px 1px 5px 0px rgba(0, 0, 0, 0.75);
  /* box-shadow: 0px 1px 5px 0px rgb(0 0 0 / 75%); */
}

.option input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  top: calc(50% - 23px);
  left: 31px;
  height: 23px;
  width: 23px;
  z-index: 1;
}

.option span {
  position: absolute;
  top: calc(50% - 20px);
  left: 10px;
  height: 25px;
  width: 25px;
  background-color: #eee;
  border-radius: 50%;
  cursor: pointer;
  z-index: 0;
}

/* On mouse-over, add a grey background color */
.option:hover input~span {
  background-color: #ccc;
}

/* When the radio button is checked, add a blue background */
.option input:checked {
  background-color: #2196F3;
}

/* Create the indicator (the dot/circle - hidden when not checked) */
.option span:after {
  content: "";
  position: absolute;
  display: none;
}

/* Show the indicator (dot/circle) when checked */
.option input:checked~.option span:after {
  display: block;
}

/* Style the indicator (dot/circle) */
.option span:after {
  top: 9px;
  left: 9px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: white;
}


[type="radio"]:checked,
[type="radio"]:not(:checked) {
  position: absolute;
  left: -9999px;
}

[type="radio"]:checked+label,
[type="radio"]:not(:checked)+label {
  position: relative;
  cursor: pointer;
  display: flex;
  width: 100%;
  height: 100%;
  margin: 0px;
  font-weight: 500;
  border-radius: 8px;
  font-size: 1rem;
  align-items: center;
  justify-content: center;
  padding: .2rem 0;
}

[type="radio"]:checked+label:before,
[type="radio"]:not(:checked)+label:before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 22px;
  height: 22px;
  border: 1px solid lightgray;
  border-radius: 100%;
  background: #fff;
  opacity: 0;
}

[type="radio"]:checked+label:after,
[type="radio"]:not(:checked)+label:after {
  content: '';
  width: 16px;
  height: 16px;
  background: #e48838;
  position: absolute;
  top: 3px;
  left: 3px;
  border-radius: 100%;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
  opacity: 0 !important;
}

[type="radio"]:not(:checked)+label:after {
  opacity: 0;
  -webkit-transform: scale(0);
  transform: scale(0);
}

[type="radio"]:checked+label:after {
  opacity: 1;
  -webkit-transform: scale(1);
  transform: scale(1);
}

.footer {
  bottom: 0px;
  padding: 10px;
  width: 80%;
  margin: auto;
  left: 100px;
  position: absolute;
}

.footer a {
  color: #030303;
}

.half-underline {
  /* content: ""; */
  position: absolute;
  background: #000;
  height: 2px;
  width: 75%;
  bottom: 0px;
}

#download {
  /* width: 50px !important; */
  /* height: 50px !important; */
  /* padding: 12px !important; */
  /* font-size: 24px; */
}

.nav-btn {
  width: 50px;
  height: 50px;
  background: transparent;
  border: 1px solid;
  border-radius: 50%;
  text-align: center;
  color: #343a40;
  /* transition: all 0.5s ease; */
}

.nav-btn:hover {
  background: #064c57;
  color: #fff;
  border-color: #064c57;
}

.congs {
  max-width: 40%;
}

.cong h5 {
  position: absolute;
  top: 35%;
  left: 6%;
  background: #fff;
  width: 90%;
  height: 50px;
}

.points img {
  width: 100px;
  margin-bottom: 20px;
}

#remember {
  margin: auto;
}

.overlay {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: #000;
  opacity: 0.5;
}

span.left-arrow * {
  color: #f1c446;
}