<?php
// Test session functionality
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Session and URL Test</h1>";

// Debug session configuration before starting
echo "<h2>Session Configuration</h2>";
echo "Session status before: " . session_status() . " (1=disabled, 2=started)<br>";
echo "Session save path (before): " . session_save_path() . "<br>";
echo "Session module name: " . session_module_name() . "<br>";

// Test if session is working
require("php/common.php");

echo "<h2>After Including common.php</h2>";
echo "Session status after: " . session_status() . " (1=disabled, 2=started)<br>";
echo "✅ Session included successfully<br>";
echo "Session ID: '" . session_id() . "'<br>";
echo "Session save path (after): " . session_save_path() . "<br>";

// Check if session directory is writable
$sessionPath = session_save_path();
if (is_dir($sessionPath)) {
    echo "Session directory exists: ✅<br>";
    if (is_writable($sessionPath)) {
        echo "Session directory is writable: ✅<br>";
    } else {
        echo "Session directory is NOT writable: ❌<br>";
    }
} else {
    echo "Session directory does NOT exist: ❌<br>";
}

echo "Current URL: " . $webUrl . "<br>";
echo "HTTP Host: " . $_SERVER['HTTP_HOST'] . "<br>";
echo "Request URI: " . $_SERVER['REQUEST_URI'] . "<br>";
echo "HTTPS Header: " . (isset($_SERVER['HTTPS']) ? $_SERVER['HTTPS'] : 'Not set') . "<br>";
echo "X-Forwarded-Proto: " . (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) ? $_SERVER['HTTP_X_FORWARDED_PROTO'] : 'Not set') . "<br>";

// Test setting and getting session data
if (!isset($_SESSION['test_counter'])) {
    $_SESSION['test_counter'] = 1;
    echo "✅ Session data set. Counter: 1<br>";
} else {
    $_SESSION['test_counter']++;
    echo "✅ Session data persisted. Counter: " . $_SESSION['test_counter'] . "<br>";
}

// Test checkSession function
echo "checkSession() returns: " . checkSession() . "<br>";

// Test database connection
echo "<h2>Database Test</h2>";
try {
    $conn = connect_db();
    if ($conn) {
        echo "✅ Database connected successfully<br>";
        
        // Test a simple query
        $result = mysqli_query($conn, "SHOW TABLES");
        if ($result) {
            echo "✅ Database query works. Tables found: " . mysqli_num_rows($result) . "<br>";
        }
        mysqli_close($conn);
    } else {
        echo "❌ Database connection failed<br>";
    }
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

echo "<br><a href='" . $_SERVER['PHP_SELF'] . "'>Reload to test session persistence</a><br>";
echo "<a href='login.php'>Back to Login</a>";
?>
