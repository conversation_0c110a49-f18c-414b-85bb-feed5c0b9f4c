"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/app/components/ui/button";
import { Download, FileText } from "lucide-react";
import PDFViewer from "@/app/components/PDFViewer";

const DashboardReportDownloader = ({ user, disabled = false }) => {
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);
  const [refreshPdfTrigger, setRefreshPdfTrigger] = useState(0);
  const [pdfReady, setPdfReady] = useState(false);  // Transform user data to match ReportDownloader expected format
  const transformUserData = (user) => {
    // The user object from the dashboard is already transformed
    // It contains the currentAttempt and user info
    let scores = null;
    let previousAttempt = null;

    // Check if user has completed second attempt
    const hasSecondAttempt = user.quizAttempts && user.quizAttempts.length >= 2;

    // If this is a transformed user row (has currentAttempt)
    if (user.currentAttempt) {
      scores = user.currentAttempt;
      
      // For second attempt, always include the first attempt for comparison
      if (hasSecondAttempt && user.attemptNumber === 2) {
        previousAttempt = user.quizAttempts[0]; // First attempt
      } else if (user.quizAttempts && user.quizAttempts.length > 1) {
        const currentIndex = user.attemptNumber - 1; // attemptNumber is 1-based
        if (currentIndex > 0) {
          previousAttempt = user.quizAttempts[currentIndex - 1];
        }
      }
    } else if (user.quizAttempts && user.quizAttempts.length > 0) {
      // Fallback: use the latest attempt
      scores = user.quizAttempts[user.quizAttempts.length - 1];
      // If user has completed second attempt, include first attempt
      if (hasSecondAttempt) {
        previousAttempt = user.quizAttempts[0]; // First attempt
      } else {
        previousAttempt = user.quizAttempts.length > 1 ? user.quizAttempts[user.quizAttempts.length - 2] : null;
      }
    } else if (user.scores) {
      // Final fallback to direct scores property
      scores = user.scores;
    }

    const userInfo = {
      name: user.name,
      age: user.age,
      gender: user.gender,
      emailId: user.email,
      contactNo: user.contact,
    };

    return { userInfo, scores, previousAttempt, hasSecondAttempt };
  };

  // Check if PDF blob is available
  useEffect(() => {
    const checkPdfBlob = () => {
      if (typeof window !== "undefined" && window.pdfBlob) {
        setPdfReady(true);
        return true;
      }
      return false;
    };

    // Check immediately
    if (!checkPdfBlob()) {
      // If not available, set up an interval to check every second
      const intervalId = setInterval(() => {
        if (checkPdfBlob()) {
          clearInterval(intervalId);
        }
      }, 1000);

      // Clean up interval
      return () => clearInterval(intervalId);
    }
  }, [refreshPdfTrigger]);
  const handleDownload = async () => {
    if (isGeneratingPDF) return;

    try {
      setIsGeneratingPDF(true);
      setPdfReady(false);

      // Refresh the PDF first to ensure it has the latest data
      setRefreshPdfTrigger((prev) => prev + 1);

      // Wait a moment for the PDF to regenerate
      await new Promise((resolve) => setTimeout(resolve, 3000));

      // Check if the PDF blob is available
      if (typeof window !== "undefined" && window.pdfBlob) {
        // Create a download link
        const url = URL.createObjectURL(window.pdfBlob);        // Create a temporary link element
        const link = document.createElement("a");
        link.href = url;
        
        // Create descriptive filename based on attempt
        let filename;
        if (hasSecondAttempt && user.attemptNumber === 2) {
          filename = `${user.name?.replace(/[^a-zA-Z0-9]/g, '_') || 'User'}_Complete_Assessment_Report_Both_Attempts_${new Date().toISOString().split('T')[0]}.pdf`;
        } else if (user.attemptNumber) {
          filename = `${user.name?.replace(/[^a-zA-Z0-9]/g, '_') || 'User'}_Assessment_Report_Attempt_${user.attemptNumber}_${new Date().toISOString().split('T')[0]}.pdf`;
        } else {
          filename = `${user.name?.replace(/[^a-zA-Z0-9]/g, '_') || 'User'}_Assessment_Report_${new Date().toISOString().split('T')[0]}.pdf`;
        }
        link.download = filename;

        // Append to the document, click it, and remove it
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Clean up the URL object
        setTimeout(() => URL.revokeObjectURL(url), 100);
      } else {
        throw new Error("PDF not ready yet. Please try again in a moment.");
      }
    } catch (error) {
      console.error("Error downloading PDF:", error);
      alert("There was an error downloading the PDF. Please try again in a moment.");
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  const { userInfo, scores, previousAttempt, hasSecondAttempt } = transformUserData(user);
  // Don't render if no scores available
  if (!scores) {
    return (
      <Button
        variant="outline"
        size="sm"
        disabled={true}
        className="opacity-50"
      >
        <FileText className="mr-1 h-3 w-3" />
        No Data
      </Button>
    );
  }
  // Determine button text based on attempt
  const getButtonText = () => {
    if (user.attemptNumber && user.attemptNumber > 0) {
      if (hasSecondAttempt && user.attemptNumber === 2) {
        return "Complete Report"; // Both attempts included
      }
      return user.attemptNumber === 1 ? "Report (1st)" : user.attemptNumber === 2 ? "Report (2nd)" : `Report (${user.attemptNumber})`;
    }
    return "Report";
  };

  return (
    <>
      <Button
        onClick={handleDownload}
        disabled={isGeneratingPDF || disabled}
        variant="outline"
        size="sm"
        className="hover:bg-[#F0C93B]/10"
      >
        {isGeneratingPDF ? (
          <>
            <svg
              className="animate-spin -ml-1 mr-1 h-3 w-3 text-current"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              ></circle>
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
            Generating...
          </>        ) : (
          <>
            <Download className="mr-1 h-3 w-3" />
            {getButtonText()}
          </>
        )}
      </Button>

      {/* PDF Generator - Hidden */}
      <div className="pdf-generator-container hidden">
        <PDFViewer
          userInfo={userInfo}
          reportScores={scores}
          reportScores2={previousAttempt}
          refreshTrigger={refreshPdfTrigger}
        />
      </div>
    </>
  );
};

export default DashboardReportDownloader;
