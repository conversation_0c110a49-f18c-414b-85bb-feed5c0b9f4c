{"name": "mending-mind", "version": "0.1.0", "private": true, "engines": {"node": ">=18.18.0"}, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "netlify-build": "DISABLE_ESLINT_PLUGIN=true npm run build"}, "dependencies": {"@barba/core": "^2.10.3", "@createnextapp/react-barcode": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.9", "@react-pdf/renderer": "^4.3.0", "@shadcn/ui": "^0.0.4", "@tanstack/react-table": "^8.13.2", "bwip-js": "^4.6.0", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "jsbarcode": "^3.11.6", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lottie-react": "^2.4.1", "lucide-react": "^0.487.0", "mongoose": "^8.2.0", "motion": "^12.6.5", "next": "15.3.0", "nodemailer": "^6.10.1", "qrcode.react": "^4.2.0", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-day-picker": "^8.10.1", "react-dom": "^19.0.0", "recharts": "^2.15.3", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.5"}, "devDependencies": {"@tailwindcss/postcss": "^4", "eslint": "^8.56.0", "eslint-config-next": "15.3.0", "tailwindcss": "^4"}, "eslintConfig": {"extends": "next", "rules": {}}}