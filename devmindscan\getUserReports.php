<?php 
include("./php/common.php");

$header="";
$data="";
$conn = connect_db();
$export = mysqli_query($conn, "select username,email,phone,gender,time_of_registration from users where date(time_of_registration) > date ('2023-03-09') and length(phone)>9");
//print_r($export);
$fields = mysqli_num_fields ( $export );

for ( $i = 0; $i < $fields; $i++ )
{

    $temp=(array)mysqli_fetch_field_direct( $export , $i );
    $header .=$temp['name'] . "\t";
}

while( $row = mysqli_fetch_row( $export ) )
{
    $line = '';
    foreach( $row as $value )
    {                           
        $value=(string)$value;
        if ( ( !isset( $value ) ) || ( $value == "" ) )
        {
            $value = "\t";
        }
        else
        {
            $value = str_replace( '"' , '""' , $value );
            $value = '"' . $value . '"' . "\t";
        }
        $line .= $value;
    }
    $data .= trim( $line ) . "\n";
}
$data = str_replace( "\r" , "" , $data );

if ( $data == "" )
{
    $data = "\n(0) Records Found!\n";                        
}
//echo $header."\n".$data;

//header("Content-type: application/octet-stream");
header("Content-Type: application/vnd.ms-excel");
header("Content-Disposition: attachment; filename=User_Details.xls");
header("Pragma: no-cache");
header("Expires: 0");
print "$header\n$data";

?>