<?php
include("./common.php");

$stress_count = ["reason" => []];
$anxiety_count = ["reason" => []];
$dep_count = ["reason" => []];

$all_reports = fetchQuery("select
user_id,
@highest_val := GREATEST(stress_score, anxiety_score, depression_score) as major,
CASE
    @highest_val
    WHEN stress_score then 'Stress'
    WHEN anxiety_score then 'Anxiety'
    WHEN depression_score Then 'Depression'
END AS major_reason
from
(
    select
        user_id,
        (
            select
                SUM(score)
            from
                answers
            where
                user_id = a.user_id
                and q_id in (
                    SELECT
                        id
                    from
                        questions
                    where
                        type = 'stress'
                )
        ) as stress_score,
        (
            select
                SUM(score)
            from
                answers
            where
                user_id = a.user_id
                and q_id in (
                    SELECT
                        id
                    from
                        questions
                    where
                        type = 'anxiety'
                )
        ) as anxiety_score,
        (
            select
                SUM(score)
            from
                answers
            where
                user_id = a.user_id
                and q_id in (
                    SELECT
                        id
                    from
                        questions
                    where
                        type = 'depression'
                )
        ) as depression_score
    from
        answers a
    where
        user_id in (
            select
                id
            from
                users
            where
                DATE(time_of_registration) = '2023-03-10'
        )
    GROUP BY
        user_id
) as data");

//print_r($all_results);


$reason_msg_chart = ["Q22" => "Self growth", "Q23" => "Family life", "Q24" => "Social life", "Q25" => "Relationship", "Q26" => "Professional achievements"];
foreach ($all_results as $result) {
    $reason = ["OPT5" => [], "OPT6" => [], "OPT7" => []];
    if ($result[2] == "Stress") {
        [$stressLevel, $stressIntensity] = getConclusion("stress", $result[1]);
        if (array_key_exists($stressLevel, $stress_count)) {
            $stress_count[$stressLevel] = $stress_count[$stressLevel] + 1;
        } else {
            $stress_count[$stressLevel] = 1;
        }
        $getanswers = fetchQuery("select a.id,b.answer as option_selected from questions a JOIN answers b on a.id=b.q_id where b.user_id='" . $result[0] . "' and a.id in ('Q22','Q23','Q24','Q25','Q26')");
        //print_r($getanswers);
        for ($i = 0; $i < count($getanswers); $i++) {
            array_push($reason[$getanswers[$i][1]], $getanswers[$i][0]);
        }
        //print_r($reason);
        if (count($reason["OPT7"]) > 0) {
            foreach ($reason["OPT7"] as $a) {
                if (array_key_exists($reason_msg_chart[$a], $stress_count["reason"])) {
                    $stress_count["reason"][$reason_msg_chart[$a]]++;
                } else {
                    $stress_count["reason"][$reason_msg_chart[$a]] = 1;
                }
            }
        } else if (count($reason["OPT6"]) > 0) {
            foreach ($reason["OPT6"] as $a) {
                if (array_key_exists($reason_msg_chart[$a], $stress_count["reason"])) {
                    $stress_count["reason"][$reason_msg_chart[$a]]++;
                } else {
                  $stress_count["reason"][$reason_msg_chart[$a]] = 1;
                }
            }
        }
    } else if ($result[2] == "Anxiety") {
        [$anxietyLevel, $anxietyIntensity] = getConclusion("anxiety", $result[1]);
        if (array_key_exists($anxietyLevel, $anxiety_count)) {
            $anxiety_count[$anxietyLevel] = $anxiety_count[$anxietyLevel] + 1;
        } else {
            $anxiety_count[$anxietyLevel] = 1;
        }
        $getanswers = fetchQuery("select a.id,b.answer as option_selected from questions a JOIN answers b on a.id=b.q_id where b.user_id='" . $result[0] . "' and a.id in ('Q22','Q23','Q24','Q25','Q26')");
        for ($i = 0; $i < count($getanswers); $i++) {
            array_push($reason[$getanswers[$i][1]], $getanswers[$i][0]);
        }
        //print_r($reason);
        if (count($reason["OPT7"]) > 0) {
            foreach ($reason["OPT7"] as $a) {
                if (array_key_exists($reason_msg_chart[$a], $anxiety_count["reason"])) {
                    $anxiety_count["reason"][$reason_msg_chart[$a]]++;
                } else {
                    $anxiety_count["reason"][$reason_msg_chart[$a]] = 1;
                }
            }
        } else if (count($reason["OPT6"]) > 0) {
            foreach ($reason["OPT6"] as $a) {
                if (array_key_exists($reason_msg_chart[$a], $anxiety_count["reason"])) {
                    $anxiety_count["reason"][$reason_msg_chart[$a]]++;
                } else {
                    $anxiety_count["reason"][$reason_msg_chart[$a]] = 1;
                }
            }
        }
    } else if ($result[2] == "Depression") {
        [$depressionLevel, $depressionIntensity] = getConclusion("depression", $result[1]);
        if (array_key_exists($depressionLevel, $dep_count)) {
            $dep_count[$depressionLevel] = $dep_count[$depressionLevel] + 1;
        } else {
            $dep_count[$depressionLevel] = 1;
        }
        $getanswers = fetchQuery("select a.id,b.answer as option_selected from questions a JOIN answers b on a.id=b.q_id where b.user_id='" . $result[0] . "' and a.id in ('Q22','Q23','Q24','Q25','Q26')");
        for ($i = 0; $i < count($getanswers); $i++) {
            array_push($reason[$getanswers[$i][1]], $getanswers[$i][0]);
        }
       //print_r($reason);
        if (count($reason["OPT7"]) > 0) {
            //echo "<br><br>".$result[0];
            foreach ($reason["OPT7"] as $a) {
                if (array_key_exists($reason_msg_chart[$a], $dep_count["reason"])) {
                    $dep_count["reason"][$reason_msg_chart[$a]]++;
                } else {
                    $dep_count["reason"][$reason_msg_chart[$a]] = 1;
                }
            }
        } else if (count($reason["OPT6"]) > 0) {
            //echo "<br><br>".$result[0];
            foreach ($reason["OPT6"] as $a) {
                if (array_key_exists($reason_msg_chart[$a], $dep_count["reason"])) {
                    $dep_count["reason"][$reason_msg_chart[$a]]++;
                } else {
                    $dep_count["reason"][$reason_msg_chart[$a]] = 1;
                }
            }
        }
    }
}


echo "~~~~~~~ Stress Count is : ~~~~~~~<br><br>";
foreach ($stress_count as $type => $count) {
    if ($type != 'reason')
        echo "<br>" . $type . "=" . $count . "<br>";
}

echo "<br><br>******* FACTORS *******<br><br>";
foreach($stress_count["reason"] as $reason=>$factor){
    echo "<br>" . $reason . "=" . $factor . "<br>";
}   

echo "<br><Br>~~~~~~~ Anxiety Count is : ~~~~~~~<br><br>";
foreach ($anxiety_count as $type => $count) {
    if ($type != 'reason')
        echo "<br>" . $type . "=" . $count . "<br>";
}
echo "<br><br>******* FACTORS *******<br><br>";
foreach($anxiety_count["reason"] as $reason=>$factor){
    echo "<br>" . $reason . "=" . $factor . "<br>";
}  
echo "<br><br>~~~~~~~ Depression Count is : ~~~~~~~ <br><br>";
foreach ($dep_count as $type => $count) {
    if ($type != 'reason')
        echo "<br>" . $type . "=" . $count . "<br>";
}
echo "<br><br>******* FACTORS *******<br><br>";
foreach($dep_count["reason"] as $reason=>$factor){
    echo "<br>" . $reason . "=" . $factor . "<br>";
}  
?>